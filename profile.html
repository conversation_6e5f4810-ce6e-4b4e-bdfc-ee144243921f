<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Digital Portfolio System</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="dashboard.html" class="nav-link">Dashboard</a></li>
                    <li><a href="profile.html" class="nav-link active">Profile</a></li>
                    <li><a href="services.html" class="nav-link">Services</a></li>
                    <li><a href="#" class="nav-link">Messages</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="Eleni Birhan" class="user-avatar">
                    <span class="user-name">Eleni Birhan</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Profile Content -->
    <main class="profile-main">
        <div class="container">
            <!-- Profile Header -->
            <div class="profile-header">
                <div class="profile-cover">
                    <div class="cover-image"></div>
                    <button class="edit-cover-btn">
                        <i class="fas fa-camera"></i>
                        Edit Cover
                    </button>
                </div>
                
                <div class="profile-info">
                    <div class="profile-avatar-section">
                        <div class="profile-avatar">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=120&h=120&fit=crop&crop=face" alt="Eleni Birhan">
                            <button class="edit-avatar-btn">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="online-indicator"></div>
                    </div>
                    
                    <div class="profile-details">
                        <h1>Eleni Birhan</h1>
                        <p class="profile-title">Full Stack Developer & UI/UX Designer</p>
                        <div class="profile-meta">
                            <div class="meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>San Francisco, CA</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Member since 2022</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-star"></i>
                                <span>4.9 (127 reviews)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-actions">
                        <button class="btn btn-primary">
                            <i class="fas fa-edit"></i>
                            Edit Profile
                        </button>
                        <button class="btn btn-outline">
                            <i class="fas fa-share"></i>
                            Share Profile
                        </button>
                    </div>
                </div>
            </div>

            <!-- Profile Content Grid -->
            <div class="profile-grid">
                <!-- About Section -->
                <div class="profile-card">
                    <div class="card-header">
                        <h3>About</h3>
                        <button class="edit-btn">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <p>Passionate full-stack developer with 5+ years of experience creating beautiful, functional web applications. I specialize in React, Node.js, and modern web technologies. I love turning complex problems into simple, beautiful designs.</p>
                        
                        <div class="profile-stats">
                            <div class="stat-item">
                                <h4>150+</h4>
                                <p>Projects Completed</p>
                            </div>
                            <div class="stat-item">
                                <h4>89</h4>
                                <p>Happy Clients</p>
                            </div>
                            <div class="stat-item">
                                <h4>5+</h4>
                                <p>Years Experience</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Skills Section -->
                <div class="profile-card">
                    <div class="card-header">
                        <h3>Skills</h3>
                        <button class="edit-btn">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="skills-grid">
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">JavaScript</span>
                                    <span class="skill-level">95%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: 95%"></div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">React</span>
                                    <span class="skill-level">90%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: 90%"></div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Node.js</span>
                                    <span class="skill-level">85%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: 85%"></div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">UI/UX Design</span>
                                    <span class="skill-level">80%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: 80%"></div>
                                </div>
                            </div>
                            
                            <div class="skill-item">
                                <div class="skill-info">
                                    <span class="skill-name">Python</span>
                                    <span class="skill-level">75%</span>
                                </div>
                                <div class="skill-bar">
                                    <div class="skill-progress" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="skill-tags">
                            <span class="skill-tag">HTML5</span>
                            <span class="skill-tag">CSS3</span>
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">PostgreSQL</span>
                            <span class="skill-tag">AWS</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">Figma</span>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Section -->
                <div class="profile-card full-width">
                    <div class="card-header">
                        <h3>Portfolio</h3>
                        <div class="portfolio-controls">
                            <button class="filter-btn active" data-filter="all">All</button>
                            <button class="filter-btn" data-filter="web">Web</button>
                            <button class="filter-btn" data-filter="mobile">Mobile</button>
                            <button class="filter-btn" data-filter="design">Design</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="portfolio-grid">
                            <div class="portfolio-item" data-category="web">
                                <div class="portfolio-image">
                                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop" alt="E-commerce Platform">
                                    <div class="portfolio-overlay">
                                        <button class="portfolio-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="portfolio-btn">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="portfolio-info">
                                    <h4>E-commerce Platform</h4>
                                    <p>Modern online shopping experience</p>
                                    <div class="portfolio-tags">
                                        <span class="tag">React</span>
                                        <span class="tag">Node.js</span>
                                        <span class="tag">MongoDB</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="portfolio-item" data-category="mobile">
                                <div class="portfolio-image">
                                    <img src="https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=400&h=300&fit=crop" alt="Mobile Banking App">
                                    <div class="portfolio-overlay">
                                        <button class="portfolio-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="portfolio-btn">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="portfolio-info">
                                    <h4>Mobile Banking App</h4>
                                    <p>Secure and intuitive banking solution</p>
                                    <div class="portfolio-tags">
                                        <span class="tag">React Native</span>
                                        <span class="tag">Firebase</span>
                                        <span class="tag">UI/UX</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="portfolio-item" data-category="design">
                                <div class="portfolio-image">
                                    <img src="https://images.unsplash.com/photo-**********-d09347e92766?w=400&h=300&fit=crop" alt="Brand Identity">
                                    <div class="portfolio-overlay">
                                        <button class="portfolio-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="portfolio-btn">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="portfolio-info">
                                    <h4>Brand Identity Design</h4>
                                    <p>Complete branding solution</p>
                                    <div class="portfolio-tags">
                                        <span class="tag">Branding</span>
                                        <span class="tag">Logo Design</span>
                                        <span class="tag">Figma</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="portfolio-item" data-category="web">
                                <div class="portfolio-image">
                                    <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=300&fit=crop" alt="Dashboard Analytics">
                                    <div class="portfolio-overlay">
                                        <button class="portfolio-btn">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="portfolio-btn">
                                            <i class="fas fa-external-link-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="portfolio-info">
                                    <h4>Analytics Dashboard</h4>
                                    <p>Data visualization platform</p>
                                    <div class="portfolio-tags">
                                        <span class="tag">Vue.js</span>
                                        <span class="tag">D3.js</span>
                                        <span class="tag">Python</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="profile-card">
                    <div class="card-header">
                        <h3>Client Reviews</h3>
                        <div class="review-summary">
                            <div class="rating-average">
                                <span class="rating-number">4.9</span>
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-count">(127 reviews)</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="reviews-list">
                            <div class="review-item">
                                <div class="review-header">
                                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face" alt="Hawinet Mekonen" class="review-avatar">
                                    <div class="review-info">
                                        <h4>Hawinet Mekonen</h4>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="review-date">2 weeks ago</span>
                                    </div>
                                </div>
                                <p>"Exceptional work! Eleni delivered exactly what we needed and more. Her attention to detail and communication throughout the project was outstanding."</p>
                            </div>
                            
                            <div class="review-item">
                                <div class="review-header">
                                    <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=50&h=50&fit=crop&crop=face" alt="Mahilet Ashenafi" class="review-avatar">
                                    <div class="review-info">
                                        <h4>Mahilet Ashenafi</h4>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="review-date">1 month ago</span>
                                    </div>
                                </div>
                                <p>"Professional, fast, and high-quality work. The mobile app design exceeded our expectations. Highly recommended!"</p>
                            </div>
                            
                            <div class="review-item">
                                <div class="review-header">
                                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face" alt="Miskir Tamire" class="review-avatar">
                                    <div class="review-info">
                                        <h4>Miskir Tamire</h4>
                                        <div class="review-rating">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="review-date">2 months ago</span>
                                    </div>
                                </div>
                                <p>"Great communication and delivered on time. The website looks amazing and functions perfectly. Will definitely work with Eleni again!"</p>
                            </div>
                        </div>
                        
                        <button class="btn btn-outline w-full mt-4">
                            View All Reviews
                        </button>
                    </div>
                </div>

                <!-- Contact Section -->
                <div class="profile-card">
                    <div class="card-header">
                        <h3>Get In Touch</h3>
                    </div>
                    <div class="card-body">
                        <div class="contact-info">
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>+****************</span>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-globe"></i>
                                <span>www.elenibirhan.dev</span>
                            </div>
                        </div>
                        
                        <div class="social-links">
                            <a href="#" class="social-link">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-dribbble"></i>
                            </a>
                        </div>
                        
                        <button class="btn btn-primary w-full">
                            <i class="fas fa-message"></i>
                            Send Message
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Back to Home -->
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
        <a href="index.html" class="btn btn-primary" style="border-radius: 50px;">
            <i class="fas fa-home"></i>
            Home
        </a>
    </div>

    <script src="scripts/main.js"></script>
    <script src="scripts/profile.js"></script>
</body>
</html>
