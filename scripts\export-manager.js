// Export Manager - Comprehensive export functionality for portfolios, projects, and data
class ExportManager {
    constructor() {
        this.exportFormats = {
            pdf: { name: 'PDF Document', icon: 'fas fa-file-pdf', color: '#dc2626' },
            csv: { name: 'CSV Spreadsheet', icon: 'fas fa-file-csv', color: '#059669' },
            json: { name: 'JSON Data', icon: 'fas fa-file-code', color: '#7c3aed' },
            html: { name: 'HTML Page', icon: 'fas fa-file-code', color: '#ea580c' },
            zip: { name: 'ZIP Archive', icon: 'fas fa-file-archive', color: '#0891b2' }
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadExportHistory();
    }

    // Setup event listeners
    setupEventListeners() {
        // Export buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-portfolio-btn') || e.target.closest('.export-portfolio-btn')) {
                this.showExportModal('portfolio');
            } else if (e.target.matches('.export-projects-btn') || e.target.closest('.export-projects-btn')) {
                this.showExportModal('projects');
            } else if (e.target.matches('.export-resume-btn') || e.target.closest('.export-resume-btn')) {
                this.showExportModal('resume');
            } else if (e.target.matches('.export-data-btn') || e.target.closest('.export-data-btn')) {
                this.showExportModal('data');
            }
        });

        // Export form submission
        const exportForm = document.getElementById('exportForm');
        if (exportForm) {
            exportForm.addEventListener('submit', (e) => this.handleExportSubmit(e));
        }
    }

    // Show export modal
    showExportModal(exportType) {
        const modal = document.getElementById('exportModal');
        if (!modal) {
            this.createExportModal();
            return this.showExportModal(exportType);
        }

        // Update modal content based on export type
        this.updateModalContent(exportType);
        modal.style.display = 'flex';
    }

    // Create export modal
    createExportModal() {
        const modal = document.createElement('div');
        modal.id = 'exportModal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h2><i class="fas fa-download"></i> Export Data</h2>
                    <button class="close-btn" onclick="exportManager.hideExportModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="exportContent">
                        <!-- Content will be dynamically loaded -->
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Update modal content based on export type
    updateModalContent(exportType) {
        const contentDiv = document.getElementById('exportContent');
        
        switch (exportType) {
            case 'portfolio':
                contentDiv.innerHTML = this.getPortfolioExportContent();
                break;
            case 'projects':
                contentDiv.innerHTML = this.getProjectsExportContent();
                break;
            case 'resume':
                contentDiv.innerHTML = this.getResumeExportContent();
                break;
            case 'data':
                contentDiv.innerHTML = this.getDataExportContent();
                break;
        }
    }

    // Get portfolio export content
    getPortfolioExportContent() {
        return `
            <form id="exportForm" data-export-type="portfolio">
                <div class="export-section">
                    <h3><i class="fas fa-user"></i> Portfolio Export</h3>
                    <p>Export your complete portfolio including profile, projects, and achievements.</p>
                </div>

                <div class="export-options">
                    <h4>Export Format</h4>
                    <div class="format-grid">
                        <label class="format-option">
                            <input type="radio" name="format" value="pdf" checked>
                            <div class="format-card">
                                <i class="fas fa-file-pdf" style="color: #dc2626;"></i>
                                <span>PDF Portfolio</span>
                                <small>Professional PDF document</small>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="html">
                            <div class="format-card">
                                <i class="fas fa-file-code" style="color: #ea580c;"></i>
                                <span>HTML Page</span>
                                <small>Standalone web page</small>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="zip">
                            <div class="format-card">
                                <i class="fas fa-file-archive" style="color: #0891b2;"></i>
                                <span>Complete Package</span>
                                <small>ZIP with all files</small>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="export-settings">
                    <h4>Include Sections</h4>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="sections" value="profile" checked>
                            <span>Profile Information</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="sections" value="projects" checked>
                            <span>Project Portfolio</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="sections" value="skills" checked>
                            <span>Skills & Expertise</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="sections" value="experience" checked>
                            <span>Work Experience</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="sections" value="education" checked>
                            <span>Education</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="sections" value="contact" checked>
                            <span>Contact Information</span>
                        </label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="exportManager.hideExportModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> Export Portfolio
                    </button>
                </div>
            </form>
        `;
    }

    // Get projects export content
    getProjectsExportContent() {
        return `
            <form id="exportForm" data-export-type="projects">
                <div class="export-section">
                    <h3><i class="fas fa-folder"></i> Projects Export</h3>
                    <p>Export your project data in various formats for analysis or backup.</p>
                </div>

                <div class="export-options">
                    <h4>Export Format</h4>
                    <div class="format-grid">
                        <label class="format-option">
                            <input type="radio" name="format" value="csv" checked>
                            <div class="format-card">
                                <i class="fas fa-file-csv" style="color: #059669;"></i>
                                <span>CSV Spreadsheet</span>
                                <small>For Excel/Sheets</small>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="json">
                            <div class="format-card">
                                <i class="fas fa-file-code" style="color: #7c3aed;"></i>
                                <span>JSON Data</span>
                                <small>Structured data format</small>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="pdf">
                            <div class="format-card">
                                <i class="fas fa-file-pdf" style="color: #dc2626;"></i>
                                <span>PDF Report</span>
                                <small>Formatted project list</small>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="export-settings">
                    <h4>Project Filters</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Status Filter</label>
                            <select name="statusFilter">
                                <option value="all">All Projects</option>
                                <option value="completed">Completed Only</option>
                                <option value="in-progress">In Progress Only</option>
                                <option value="planned">Planned Only</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Date Range</label>
                            <select name="dateRange">
                                <option value="all">All Time</option>
                                <option value="last-month">Last Month</option>
                                <option value="last-3-months">Last 3 Months</option>
                                <option value="last-year">Last Year</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="exportManager.hideExportModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> Export Projects
                    </button>
                </div>
            </form>
        `;
    }

    // Get resume export content
    getResumeExportContent() {
        return `
            <form id="exportForm" data-export-type="resume">
                <div class="export-section">
                    <h3><i class="fas fa-file-alt"></i> Resume Export</h3>
                    <p>Generate a professional resume from your portfolio data.</p>
                </div>

                <div class="export-options">
                    <h4>Resume Template</h4>
                    <div class="template-grid">
                        <label class="template-option">
                            <input type="radio" name="template" value="modern" checked>
                            <div class="template-card">
                                <div class="template-preview modern"></div>
                                <span>Modern</span>
                            </div>
                        </label>
                        <label class="template-option">
                            <input type="radio" name="template" value="classic">
                            <div class="template-card">
                                <div class="template-preview classic"></div>
                                <span>Classic</span>
                            </div>
                        </label>
                        <label class="template-option">
                            <input type="radio" name="template" value="creative">
                            <div class="template-card">
                                <div class="template-preview creative"></div>
                                <span>Creative</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="export-settings">
                    <h4>Resume Sections</h4>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="resumeSections" value="summary" checked>
                            <span>Professional Summary</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resumeSections" value="experience" checked>
                            <span>Work Experience</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resumeSections" value="projects" checked>
                            <span>Key Projects</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resumeSections" value="skills" checked>
                            <span>Technical Skills</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resumeSections" value="education" checked>
                            <span>Education</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="resumeSections" value="certifications">
                            <span>Certifications</span>
                        </label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="exportManager.hideExportModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> Generate Resume
                    </button>
                </div>
            </form>
        `;
    }

    // Get data export content
    getDataExportContent() {
        return `
            <form id="exportForm" data-export-type="data">
                <div class="export-section">
                    <h3><i class="fas fa-database"></i> Data Export</h3>
                    <p>Export all your portfolio data for backup or migration purposes.</p>
                </div>

                <div class="export-options">
                    <h4>Data Format</h4>
                    <div class="format-grid">
                        <label class="format-option">
                            <input type="radio" name="format" value="json" checked>
                            <div class="format-card">
                                <i class="fas fa-file-code" style="color: #7c3aed;"></i>
                                <span>JSON Backup</span>
                                <small>Complete data backup</small>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="zip">
                            <div class="format-card">
                                <i class="fas fa-file-archive" style="color: #0891b2;"></i>
                                <span>Full Backup</span>
                                <small>Data + uploaded files</small>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="export-settings">
                    <h4>Include Data</h4>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="dataTypes" value="profile" checked>
                            <span>Profile Data</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="dataTypes" value="projects" checked>
                            <span>Project Data</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="dataTypes" value="calendar" checked>
                            <span>Calendar Events</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="dataTypes" value="messages" checked>
                            <span>Messages</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="dataTypes" value="settings" checked>
                            <span>Settings</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" name="dataTypes" value="files">
                            <span>Uploaded Files</span>
                        </label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="exportManager.hideExportModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                </div>
            </form>
        `;
    }

    // Hide export modal
    hideExportModal() {
        const modal = document.getElementById('exportModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    // Handle export form submission
    handleExportSubmit(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const exportType = form.dataset.exportType;
        const format = formData.get('format');

        this.showExportProgress();

        // Simulate processing delay
        setTimeout(() => {
            this.processExport(exportType, formData);
        }, 1000);
    }

    // Show export progress
    showExportProgress() {
        const modal = document.getElementById('exportModal');
        const content = modal.querySelector('.modal-body');

        content.innerHTML = `
            <div class="export-progress">
                <div class="progress-icon">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
                <h3>Preparing Export...</h3>
                <p>Please wait while we generate your export file.</p>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        `;

        // Animate progress bar
        setTimeout(() => {
            const progressFill = content.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = '100%';
            }
        }, 500);
    }

    // Process export based on type and format
    processExport(exportType, formData) {
        const format = formData.get('format');

        switch (exportType) {
            case 'portfolio':
                this.exportPortfolio(formData);
                break;
            case 'projects':
                this.exportProjects(formData);
                break;
            case 'resume':
                this.exportResume(formData);
                break;
            case 'data':
                this.exportData(formData);
                break;
        }
    }

    // Export portfolio
    exportPortfolio(formData) {
        const format = formData.get('format');
        const sections = formData.getAll('sections');
        const currentUser = authSystem.getCurrentUser();

        if (!currentUser) {
            this.showExportError('User not authenticated');
            return;
        }

        const portfolioData = this.gatherPortfolioData(currentUser, sections);

        switch (format) {
            case 'pdf':
                this.generatePortfolioPDF(portfolioData);
                break;
            case 'html':
                this.generatePortfolioHTML(portfolioData);
                break;
            case 'zip':
                this.generatePortfolioZIP(portfolioData);
                break;
        }
    }

    // Export projects
    exportProjects(formData) {
        const format = formData.get('format');
        const statusFilter = formData.get('statusFilter');
        const dateRange = formData.get('dateRange');
        const currentUser = authSystem.getCurrentUser();

        if (!currentUser) {
            this.showExportError('User not authenticated');
            return;
        }

        const projectsData = this.gatherProjectsData(currentUser, statusFilter, dateRange);

        switch (format) {
            case 'csv':
                this.generateProjectsCSV(projectsData);
                break;
            case 'json':
                this.generateProjectsJSON(projectsData);
                break;
            case 'pdf':
                this.generateProjectsPDF(projectsData);
                break;
        }
    }

    // Export resume
    exportResume(formData) {
        const template = formData.get('template');
        const sections = formData.getAll('resumeSections');
        const currentUser = authSystem.getCurrentUser();

        if (!currentUser) {
            this.showExportError('User not authenticated');
            return;
        }

        const resumeData = this.gatherResumeData(currentUser, sections);
        this.generateResumePDF(resumeData, template);
    }

    // Export data
    exportData(formData) {
        const format = formData.get('format');
        const dataTypes = formData.getAll('dataTypes');
        const currentUser = authSystem.getCurrentUser();

        if (!currentUser) {
            this.showExportError('User not authenticated');
            return;
        }

        const allData = this.gatherAllData(currentUser, dataTypes);

        switch (format) {
            case 'json':
                this.generateDataJSON(allData);
                break;
            case 'zip':
                this.generateDataZIP(allData);
                break;
        }
    }

    // Gather portfolio data
    gatherPortfolioData(user, sections) {
        const data = {
            exportDate: new Date().toISOString(),
            user: {
                id: user.id,
                fullName: user.fullName,
                email: user.email,
                role: user.role
            }
        };

        if (sections.includes('profile')) {
            data.profile = {
                name: user.fullName,
                title: 'Full Stack Developer & UI/UX Designer', // This would come from user profile
                location: 'San Francisco, CA',
                bio: 'Passionate developer with expertise in modern web technologies...',
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
            };
        }

        if (sections.includes('projects')) {
            const projects = JSON.parse(localStorage.getItem(`portfolio_projects_${user.id}`) || '[]');
            data.projects = projects;
        }

        if (sections.includes('skills')) {
            data.skills = [
                'JavaScript', 'React', 'Node.js', 'Python', 'UI/UX Design',
                'HTML/CSS', 'MongoDB', 'PostgreSQL', 'Git', 'Docker'
            ];
        }

        if (sections.includes('experience')) {
            data.experience = [
                {
                    title: 'Senior Full Stack Developer',
                    company: 'Tech Solutions Inc.',
                    period: '2022 - Present',
                    description: 'Led development of multiple web applications using React and Node.js'
                },
                {
                    title: 'Frontend Developer',
                    company: 'Digital Agency',
                    period: '2020 - 2022',
                    description: 'Developed responsive web interfaces and improved user experience'
                }
            ];
        }

        if (sections.includes('education')) {
            data.education = [
                {
                    degree: 'Bachelor of Computer Science',
                    school: 'University of Technology',
                    year: '2020',
                    gpa: '3.8/4.0'
                }
            ];
        }

        if (sections.includes('contact')) {
            data.contact = {
                email: user.email,
                phone: '+****************',
                website: 'https://portfolio.example.com',
                linkedin: 'https://linkedin.com/in/example',
                github: 'https://github.com/example'
            };
        }

        return data;
    }

    // Gather projects data
    gatherProjectsData(user, statusFilter, dateRange) {
        let projects = JSON.parse(localStorage.getItem(`portfolio_projects_${user.id}`) || '[]');

        // Apply status filter
        if (statusFilter !== 'all') {
            projects = projects.filter(project => project.status === statusFilter);
        }

        // Apply date range filter
        if (dateRange !== 'all') {
            const now = new Date();
            let cutoffDate;

            switch (dateRange) {
                case 'last-month':
                    cutoffDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    break;
                case 'last-3-months':
                    cutoffDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                    break;
                case 'last-year':
                    cutoffDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                    break;
            }

            if (cutoffDate) {
                projects = projects.filter(project => new Date(project.createdAt) >= cutoffDate);
            }
        }

        return {
            exportDate: new Date().toISOString(),
            user: user.fullName,
            filters: { statusFilter, dateRange },
            projects: projects,
            summary: {
                totalProjects: projects.length,
                completedProjects: projects.filter(p => p.status === 'completed').length,
                inProgressProjects: projects.filter(p => p.status === 'in-progress').length,
                plannedProjects: projects.filter(p => p.status === 'planned').length
            }
        };
    }

    // Generate Projects CSV
    generateProjectsCSV(data) {
        const headers = ['Title', 'Category', 'Status', 'Description', 'Skills', 'URL', 'GitHub', 'Created Date'];
        const rows = [headers];

        data.projects.forEach(project => {
            rows.push([
                project.title || '',
                project.category || '',
                project.status || '',
                project.description || '',
                (project.skills || []).join('; '),
                project.url || '',
                project.githubUrl || '',
                project.createdAt ? new Date(project.createdAt).toLocaleDateString() : ''
            ]);
        });

        const csvContent = rows.map(row =>
            row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
        ).join('\n');

        this.downloadFile(csvContent, `projects-export-${this.getDateString()}.csv`, 'text/csv');
        this.showExportSuccess('Projects exported successfully!');
    }

    // Generate Projects JSON
    generateProjectsJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        this.downloadFile(jsonContent, `projects-export-${this.getDateString()}.json`, 'application/json');
        this.showExportSuccess('Projects exported successfully!');
    }

    // Generate Portfolio HTML
    generatePortfolioHTML(data) {
        const htmlContent = this.createPortfolioHTML(data);
        this.downloadFile(htmlContent, `portfolio-${this.getDateString()}.html`, 'text/html');
        this.showExportSuccess('Portfolio exported successfully!');
    }

    // Create Portfolio HTML
    createPortfolioHTML(data) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.profile?.name || 'Portfolio'}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; padding: 40px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .profile-img { width: 150px; height: 150px; border-radius: 50%; margin: 0 auto 20px; object-fit: cover; }
        .section { margin-bottom: 40px; }
        .section h2 { color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; margin-bottom: 20px; }
        .project-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .project-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: #f9f9f9; }
        .skills { display: flex; flex-wrap: wrap; gap: 10px; }
        .skill { background: #667eea; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.9em; }
        .contact-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
    </style>
</head>
<body>
    <div class="header">
        ${data.profile?.avatar ? `<img src="${data.profile.avatar}" alt="Profile" class="profile-img">` : ''}
        <h1>${data.profile?.name || 'Portfolio'}</h1>
        <p>${data.profile?.title || ''}</p>
        <p>${data.profile?.location || ''}</p>
    </div>

    <div class="container">
        ${data.profile?.bio ? `
        <div class="section">
            <h2>About</h2>
            <p>${data.profile.bio}</p>
        </div>
        ` : ''}

        ${data.projects && data.projects.length > 0 ? `
        <div class="section">
            <h2>Projects</h2>
            <div class="project-grid">
                ${data.projects.map(project => `
                    <div class="project-card">
                        <h3>${project.title}</h3>
                        <p><strong>Category:</strong> ${project.category}</p>
                        <p><strong>Status:</strong> ${project.status}</p>
                        <p>${project.description}</p>
                        ${project.skills && project.skills.length > 0 ? `
                            <div class="skills">
                                ${project.skills.map(skill => `<span class="skill">${skill}</span>`).join('')}
                            </div>
                        ` : ''}
                        ${project.url ? `<p><a href="${project.url}" target="_blank">View Project</a></p>` : ''}
                        ${project.githubUrl ? `<p><a href="${project.githubUrl}" target="_blank">GitHub</a></p>` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        ${data.skills && data.skills.length > 0 ? `
        <div class="section">
            <h2>Skills</h2>
            <div class="skills">
                ${data.skills.map(skill => `<span class="skill">${skill}</span>`).join('')}
            </div>
        </div>
        ` : ''}

        ${data.experience && data.experience.length > 0 ? `
        <div class="section">
            <h2>Experience</h2>
            ${data.experience.map(exp => `
                <div style="margin-bottom: 20px;">
                    <h3>${exp.title}</h3>
                    <p><strong>${exp.company}</strong> | ${exp.period}</p>
                    <p>${exp.description}</p>
                </div>
            `).join('')}
        </div>
        ` : ''}

        ${data.education && data.education.length > 0 ? `
        <div class="section">
            <h2>Education</h2>
            ${data.education.map(edu => `
                <div style="margin-bottom: 20px;">
                    <h3>${edu.degree}</h3>
                    <p><strong>${edu.school}</strong> | ${edu.year}</p>
                    ${edu.gpa ? `<p>GPA: ${edu.gpa}</p>` : ''}
                </div>
            `).join('')}
        </div>
        ` : ''}

        ${data.contact ? `
        <div class="section">
            <h2>Contact</h2>
            <div class="contact-info">
                ${data.contact.email ? `<p><strong>Email:</strong> ${data.contact.email}</p>` : ''}
                ${data.contact.phone ? `<p><strong>Phone:</strong> ${data.contact.phone}</p>` : ''}
                ${data.contact.website ? `<p><strong>Website:</strong> <a href="${data.contact.website}">${data.contact.website}</a></p>` : ''}
                ${data.contact.linkedin ? `<p><strong>LinkedIn:</strong> <a href="${data.contact.linkedin}">Profile</a></p>` : ''}
                ${data.contact.github ? `<p><strong>GitHub:</strong> <a href="${data.contact.github}">Profile</a></p>` : ''}
            </div>
        </div>
        ` : ''}
    </div>

    <footer style="text-align: center; padding: 20px; color: #666; border-top: 1px solid #ddd; margin-top: 40px;">
        <p>Generated on ${new Date().toLocaleDateString()} | PortfolioPro Export</p>
    </footer>
</body>
</html>`;
    }

    // Generate Data JSON
    generateDataJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        this.downloadFile(jsonContent, `portfolio-data-backup-${this.getDateString()}.json`, 'application/json');
        this.showExportSuccess('Data exported successfully!');
    }

    // Gather all data for backup
    gatherAllData(user, dataTypes) {
        const data = {
            exportDate: new Date().toISOString(),
            version: '1.0',
            user: {
                id: user.id,
                fullName: user.fullName,
                email: user.email,
                role: user.role
            }
        };

        if (dataTypes.includes('profile')) {
            data.profile = this.gatherPortfolioData(user, ['profile']).profile;
        }

        if (dataTypes.includes('projects')) {
            data.projects = JSON.parse(localStorage.getItem(`portfolio_projects_${user.id}`) || '[]');
        }

        if (dataTypes.includes('calendar')) {
            data.calendar = JSON.parse(localStorage.getItem(`calendar_events_${user.id}`) || '[]');
        }

        if (dataTypes.includes('messages')) {
            data.messages = JSON.parse(localStorage.getItem(`messages_${user.id}`) || '[]');
        }

        if (dataTypes.includes('settings')) {
            data.settings = JSON.parse(localStorage.getItem(`settings_${user.id}`) || '{}');
        }

        return data;
    }

    // Utility methods
    getDateString() {
        return new Date().toISOString().split('T')[0];
    }

    downloadFile(content, filename, mimeType = 'text/plain') {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Log export to history
        this.logExport(filename, mimeType);
    }

    logExport(filename, type) {
        const exports = JSON.parse(localStorage.getItem('export_history') || '[]');
        exports.unshift({
            filename,
            type,
            date: new Date().toISOString(),
            user: authSystem.getCurrentUser()?.fullName || 'Unknown'
        });

        // Keep only last 50 exports
        if (exports.length > 50) {
            exports.splice(50);
        }

        localStorage.setItem('export_history', JSON.stringify(exports));
    }

    loadExportHistory() {
        return JSON.parse(localStorage.getItem('export_history') || '[]');
    }

    showExportSuccess(message) {
        this.hideExportModal();
        this.showNotification(message, 'success');
    }

    showExportError(message) {
        this.hideExportModal();
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize export manager
let exportManager;
document.addEventListener('DOMContentLoaded', function() {
    exportManager = new ExportManager();
});
