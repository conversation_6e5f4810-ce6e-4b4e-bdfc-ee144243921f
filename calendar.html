<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="freelancer-dashboard.html" class="nav-link">Dashboard</a></li>
                    <li><a href="profile.html" class="nav-link">Profile</a></li>
                    <li><a href="calendar.html" class="nav-link active">Calendar</a></li>
                    <li><a href="#" class="nav-link">Messages</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="User" class="user-avatar">
                    <span class="user-name">Eleni Birhan</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Calendar Content -->
    <main class="calendar-main">
        <div class="container">
            <!-- Calendar Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1><i class="fas fa-calendar-alt"></i> Calendar</h1>
                    <p>Manage your schedule, meetings, and deadlines</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-outline" onclick="calendarManager.exportCalendar('ics')">
                        <i class="fas fa-download"></i> Export Calendar
                    </button>
                    <button class="btn btn-primary add-event-btn">
                        <i class="fas fa-plus"></i> Add Event
                    </button>
                </div>
            </div>

            <!-- Calendar Layout -->
            <div class="calendar-layout">
                <!-- Calendar Container -->
                <div class="calendar-section">
                    <div id="calendarContainer" class="calendar-container">
                        <!-- Calendar will be rendered here -->
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="calendar-sidebar">
                    <div id="calendarSidebar" class="sidebar-content">
                        <div class="sidebar-header">
                            <h3>Select a date</h3>
                            <p>Click on a date to view events</p>
                        </div>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="upcoming-events">
                        <h3>Upcoming Events</h3>
                        <div id="upcomingEventsList" class="upcoming-list">
                            <!-- Upcoming events will be loaded here -->
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="calendar-stats">
                        <h3>This Month</h3>
                        <div class="stats-list">
                            <div class="stat-item">
                                <div class="stat-icon meeting">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number" id="meetingCount">0</span>
                                    <span class="stat-label">Meetings</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon deadline">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number" id="deadlineCount">0</span>
                                    <span class="stat-label">Deadlines</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon reminder">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number" id="reminderCount">0</span>
                                    <span class="stat-label">Reminders</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Event Modal -->
    <div id="addEventModal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2><i class="fas fa-calendar-plus"></i> Add New Event</h2>
                <button class="close-btn" onclick="calendarManager.hideAddEventModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="eventForm" class="event-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="eventTitle">Event Title *</label>
                        <input type="text" id="eventTitle" name="eventTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="eventType">Event Type *</label>
                        <select id="eventType" name="eventType" required>
                            <option value="meeting">Meeting</option>
                            <option value="deadline">Deadline</option>
                            <option value="reminder">Reminder</option>
                            <option value="personal">Personal</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="eventDescription">Description</label>
                    <textarea id="eventDescription" name="eventDescription" rows="3" 
                              placeholder="Add event details, agenda, or notes..."></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="eventDate">Date *</label>
                        <input type="date" id="eventDate" name="eventDate" required>
                    </div>
                    <div class="form-group">
                        <label for="startTime">Start Time *</label>
                        <input type="time" id="startTime" name="startTime" required>
                    </div>
                    <div class="form-group">
                        <label for="endTime">End Time *</label>
                        <input type="time" id="endTime" name="endTime" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="eventLocation">Location</label>
                        <input type="text" id="eventLocation" name="eventLocation" 
                               placeholder="Meeting room, Zoom link, address...">
                    </div>
                    <div class="form-group">
                        <label for="eventPriority">Priority</label>
                        <select id="eventPriority" name="eventPriority">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="eventAttendees">Attendees (Email addresses)</label>
                    <input type="text" id="eventAttendees" name="eventAttendees" 
                           placeholder="<EMAIL>, <EMAIL>">
                    <small class="form-help">Separate multiple email addresses with commas</small>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="calendarManager.hideAddEventModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Event
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Back to Dashboard -->
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
        <a href="freelancer-dashboard.html" class="btn btn-primary" style="border-radius: 50px;">
            <i class="fas fa-arrow-left"></i>
            Dashboard
        </a>
    </div>

    <script src="scripts/auth.js"></script>
    <script src="scripts/calendar-manager.js"></script>
    <script src="scripts/export-manager.js"></script>
    <script>
        // Initialize calendar page
        document.addEventListener('DOMContentLoaded', function() {
            // Load upcoming events
            if (typeof calendarManager !== 'undefined') {
                setTimeout(() => {
                    loadUpcomingEvents();
                    updateCalendarStats();
                }, 500);
            }
        });

        function loadUpcomingEvents() {
            const upcomingEvents = calendarManager.getUpcomingEvents(7);
            const container = document.getElementById('upcomingEventsList');
            
            if (upcomingEvents.length === 0) {
                container.innerHTML = '<div class="no-events">No upcoming events</div>';
                return;
            }

            container.innerHTML = upcomingEvents.map(event => {
                const eventDate = new Date(event.startDate);
                const isToday = calendarManager.isToday(eventDate);
                const timeStr = eventDate.toLocaleTimeString('en-US', { 
                    hour: 'numeric', 
                    minute: '2-digit',
                    hour12: true 
                });
                
                return `
                    <div class="upcoming-event" data-event-id="${event.id}">
                        <div class="event-indicator" style="background-color: ${calendarManager.eventTypes[event.type].color}"></div>
                        <div class="event-content">
                            <h4 class="event-title">${event.title}</h4>
                            <div class="event-time">
                                ${isToday ? 'Today' : eventDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} at ${timeStr}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function updateCalendarStats() {
            const now = new Date();
            const currentMonth = now.getMonth();
            const currentYear = now.getFullYear();
            
            const monthEvents = calendarManager.events.filter(event => {
                const eventDate = new Date(event.startDate);
                return eventDate.getMonth() === currentMonth && eventDate.getFullYear() === currentYear;
            });

            const meetingCount = monthEvents.filter(e => e.type === 'meeting').length;
            const deadlineCount = monthEvents.filter(e => e.type === 'deadline').length;
            const reminderCount = monthEvents.filter(e => e.type === 'reminder').length;

            document.getElementById('meetingCount').textContent = meetingCount;
            document.getElementById('deadlineCount').textContent = deadlineCount;
            document.getElementById('reminderCount').textContent = reminderCount;
        }
    </script>
</body>
</html>
