// Freelancer Dashboard JavaScript
// Handles freelancer-specific functionality, job browsing, proposal submission, and project management

class FreelancerDashboard {
    constructor() {
        this.jobs = [];
        this.proposals = [];
        this.activeProjects = [];
        this.init();
    }

    init() {
        // Check freelancer access
        if (!authSystem.requireAuth('freelancer')) {
            return;
        }

        this.loadUserInfo();
        this.loadDashboardData();
        this.setupEventListeners();
        this.initializeCharts();
    }

    loadUserInfo() {
        const currentUser = authSystem.getCurrentUser();
        if (currentUser) {
            // Update user name in header
            const userNameElements = document.querySelectorAll('.user-name');
            userNameElements.forEach(element => {
                element.textContent = currentUser.fullName;
            });

            // Update welcome message
            const welcomeMessage = document.querySelector('.welcome-section h1');
            if (welcomeMessage) {
                welcomeMessage.textContent = `Welcome back, ${currentUser.fullName} 👋`;
            }

            // Update user avatar
            const userAvatars = document.querySelectorAll('.user-avatar');
            userAvatars.forEach(avatar => {
                if (currentUser.profileData && currentUser.profileData.avatar) {
                    avatar.src = currentUser.profileData.avatar;
                }
            });
        }
    }

    loadDashboardData() {
        // Load freelancer-specific data
        this.loadAvailableJobs();
        this.loadMyProposals();
        this.loadActiveProjects();
        this.updateStats();
    }

    loadAvailableJobs() {
        // Mock job data - in real app, this would come from API
        this.jobs = [
            {
                id: 1,
                title: "E-commerce Website Development",
                description: "Looking for a skilled developer to build a modern e-commerce platform",
                budget: "$2,500 - $5,000",
                deadline: "2 weeks",
                client: "Hawinet Mekonen",
                skills: ["React", "Node.js", "MongoDB"],
                posted: "2 hours ago"
            },
            {
                id: 2,
                title: "Mobile App UI/UX Design",
                description: "Need a creative designer for iOS and Android app interface",
                budget: "$1,200 - $2,000",
                deadline: "1 week",
                client: "Mahilet Ashenafi",
                skills: ["Figma", "UI/UX", "Mobile Design"],
                posted: "5 hours ago"
            },
            {
                id: 3,
                title: "Brand Identity Package",
                description: "Complete brand identity including logo, colors, and guidelines",
                budget: "$800 - $1,500",
                deadline: "10 days",
                client: "Miskir Tamire",
                skills: ["Graphic Design", "Branding", "Adobe Creative Suite"],
                posted: "1 day ago"
            }
        ];
    }

    loadMyProposals() {
        // Mock proposal data
        this.proposals = [
            {
                id: 1,
                jobTitle: "Corporate Website Redesign",
                client: "Eleni Birhan",
                status: "pending",
                submittedDate: "2024-01-15",
                proposedAmount: "$3,200"
            },
            {
                id: 2,
                jobTitle: "Logo Design Project",
                client: "Hawinet Mekonen",
                status: "accepted",
                submittedDate: "2024-01-12",
                proposedAmount: "$650"
            }
        ];
    }

    loadActiveProjects() {
        // Mock active projects data
        this.activeProjects = [
            {
                id: 1,
                title: "E-commerce Platform",
                client: "Mahilet Ashenafi",
                progress: 75,
                deadline: "2024-02-15",
                value: "$4,200"
            },
            {
                id: 2,
                title: "Mobile App Design",
                client: "Miskir Tamire",
                progress: 45,
                deadline: "2024-02-20",
                value: "$1,800"
            }
        ];
    }

    updateStats() {
        // Update dashboard statistics
        const stats = {
            activeProjects: this.activeProjects.length,
            totalEarnings: 8450,
            averageRating: 4.9,
            completedProjects: 23
        };

        // Update stat cards if they exist
        const statElements = {
            activeProjects: document.querySelector('[data-stat="active-projects"]'),
            totalEarnings: document.querySelector('[data-stat="total-earnings"]'),
            averageRating: document.querySelector('[data-stat="average-rating"]'),
            completedProjects: document.querySelector('[data-stat="completed-projects"]')
        };

        Object.keys(statElements).forEach(key => {
            const element = statElements[key];
            if (element) {
                if (key === 'totalEarnings') {
                    element.textContent = `$${stats[key].toLocaleString()}`;
                } else if (key === 'averageRating') {
                    element.textContent = `${stats[key]} ⭐`;
                } else {
                    element.textContent = stats[key];
                }
            }
        });
    }

    setupEventListeners() {
        // Job application buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.apply-job-btn')) {
                const jobId = e.target.dataset.jobId;
                this.showJobApplicationModal(jobId);
            }
        });

        // Project management buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-project-btn')) {
                const projectId = e.target.dataset.projectId;
                this.viewProjectDetails(projectId);
            }
        });

        // Logout button
        document.addEventListener('click', (e) => {
            if (e.target.matches('.logout-btn') || e.target.closest('.logout-btn')) {
                e.preventDefault();
                authSystem.logout();
            }
        });

        // Quick action buttons
        const quickActionBtns = document.querySelectorAll('.btn-primary');
        quickActionBtns.forEach(btn => {
            if (btn.textContent.includes('New Project')) {
                btn.addEventListener('click', () => this.showNewProjectModal());
            }
        });
    }

    showJobApplicationModal(jobId) {
        const job = this.jobs.find(j => j.id == jobId);
        if (!job) return;

        const modal = this.createModal('Apply for Job', `
            <div class="job-application-modal">
                <h3>${job.title}</h3>
                <p><strong>Client:</strong> ${job.client}</p>
                <p><strong>Budget:</strong> ${job.budget}</p>
                <form id="jobApplicationForm">
                    <div class="form-group">
                        <label>Cover Letter</label>
                        <textarea class="form-input" rows="4" placeholder="Explain why you're the perfect fit for this project..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label>Proposed Amount ($)</label>
                        <input type="number" class="form-input" placeholder="Your bid amount" required>
                    </div>
                    <div class="form-group">
                        <label>Delivery Time</label>
                        <select class="form-input" required>
                            <option value="">Select delivery time</option>
                            <option value="3">3 days</option>
                            <option value="7">1 week</option>
                            <option value="14">2 weeks</option>
                            <option value="30">1 month</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary w-full">Submit Proposal</button>
                </form>
            </div>
        `);

        document.body.appendChild(modal);
        modal.style.display = 'block';

        // Handle form submission
        const form = modal.querySelector('#jobApplicationForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitJobApplication(jobId, new FormData(form));
            modal.remove();
        });
    }

    submitJobApplication(jobId, formData) {
        // In real app, this would send to API
        console.log('Submitting job application for job:', jobId);
        alert('Proposal submitted successfully! The client will review your application.');
    }

    viewProjectDetails(projectId) {
        const project = this.activeProjects.find(p => p.id == projectId);
        if (!project) return;

        alert(`Project Details: ${project.title}\n\nClient: ${project.client}\nProgress: ${project.progress}%\nDeadline: ${project.deadline}\nValue: ${project.value}`);
    }

    showNewProjectModal() {
        alert('New Project Creation\n\nThis feature allows you to create personal projects for your portfolio. Coming soon!');
    }

    initializeCharts() {
        // Initialize any charts or visualizations
        console.log('Initializing freelancer dashboard charts...');
    }

    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        `;
        
        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                margin: 5% auto;
                padding: 20px;
                border-radius: 8px;
                width: 90%;
                max-width: 500px;
                position: relative;
            ">
                <span class="close" style="
                    position: absolute;
                    right: 15px;
                    top: 15px;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                ">&times;</span>
                <h2 style="margin-bottom: 20px;">${title}</h2>
                ${content}
            </div>
        `;

        // Close modal functionality
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => modal.remove());
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }
}

// Initialize freelancer dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Freelancer Dashboard...');
    new FreelancerDashboard();
});

console.log('Freelancer Dashboard JavaScript loaded successfully!');
