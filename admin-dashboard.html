<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro Admin</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="admin-dashboard.html" class="nav-link active">Dashboard</a></li>
                    <li><a href="#" class="nav-link">Users</a></li>
                    <li><a href="#" class="nav-link">Projects</a></li>
                    <li><a href="#" class="nav-link">Analytics</a></li>
                    <li><a href="#" class="nav-link">Settings</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="Admin" class="user-avatar">
                    <span class="user-name">Admin</span>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">Profile</a>
                        <a href="#" class="dropdown-item">Settings</a>
                        <a href="#" class="dropdown-item logout-btn">Logout</a>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Admin Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1>Admin Dashboard 🛡️</h1>
                    <p>Manage your platform and monitor system performance</p>
                </div>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="showUserManagement()">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </button>
                    <button class="btn btn-outline" onclick="showSystemSettings()">
                        <i class="fas fa-cog"></i>
                        Settings
                    </button>
                </div>
            </div>

            <!-- Admin Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card glass-card">
                    <div class="stat-icon admin-stat">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalUsers">0</h3>
                        <p>Total Users</p>
                        <span class="stat-change positive">+5 this week</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon admin-stat">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeFreelancers">0</h3>
                        <p>Active Freelancers</p>
                        <span class="stat-change neutral" id="pendingFreelancers">2 pending approval</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon admin-stat">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeProjects">0</h3>
                        <p>Active Projects</p>
                        <span class="stat-change positive">+12% this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon admin-stat">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>$24,580</h3>
                        <p>Platform Revenue</p>
                        <span class="stat-change positive">+18% this month</span>
                    </div>
                </div>
            </div>

            <!-- Admin Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Pending Approvals -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Pending Freelancer Approvals</h3>
                        <span class="badge" id="pendingCount">0</span>
                    </div>
                    <div class="card-body">
                        <div id="pendingApprovals" class="approval-list">
                            <!-- Pending approvals will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Activity</h3>
                        <a href="#" class="view-all">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <p><strong>New user registered:</strong> Hawinet Mekonen (Freelancer)</p>
                                    <span class="activity-time">2 hours ago</span>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="activity-content">
                                    <p><strong>Project completed:</strong> E-commerce Website</p>
                                    <span class="activity-time">4 hours ago</span>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="activity-content">
                                    <p><strong>Payment processed:</strong> $1,200 for Mobile App Design</p>
                                    <span class="activity-time">6 hours ago</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Health -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>System Health</h3>
                        <span class="status-indicator online"></span>
                    </div>
                    <div class="card-body">
                        <div class="health-metrics">
                            <div class="metric-item">
                                <span class="metric-label">Server Status</span>
                                <span class="metric-value online">Online</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Database</span>
                                <span class="metric-value online">Connected</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Payment Gateway</span>
                                <span class="metric-value online">Active</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Email Service</span>
                                <span class="metric-value online">Operational</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions-grid">
                            <button class="quick-action-btn" onclick="showUserManagement()">
                                <i class="fas fa-users"></i>
                                <span>Manage Users</span>
                            </button>
                            <button class="quick-action-btn" onclick="showReports()">
                                <i class="fas fa-chart-bar"></i>
                                <span>View Reports</span>
                            </button>
                            <button class="quick-action-btn" onclick="showDisputes()">
                                <i class="fas fa-gavel"></i>
                                <span>Handle Disputes</span>
                            </button>
                            <button class="quick-action-btn" onclick="showSystemSettings()">
                                <i class="fas fa-cog"></i>
                                <span>System Settings</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- User Management Modal -->
    <div id="userManagementModal" class="modal">
        <div class="modal-content large-modal">
            <span class="close" onclick="closeModal('userManagementModal')">&times;</span>
            <h2>User Management</h2>
            <div class="user-management-content">
                <div class="user-filters">
                    <select id="roleFilter" class="form-input">
                        <option value="">All Roles</option>
                        <option value="freelancer">Freelancers</option>
                        <option value="client">Clients</option>
                    </select>
                    <select id="statusFilter" class="form-input">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
                <div id="usersList" class="users-list">
                    <!-- Users will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/auth.js"></script>
    <script src="scripts/admin-dashboard.js"></script>
</body>
</html>
