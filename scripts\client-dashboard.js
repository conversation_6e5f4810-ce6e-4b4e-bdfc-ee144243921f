// Client Dashboard JavaScript
// Handles client-specific functionality, project posting, freelancer browsing, and project management

class ClientDashboard {
    constructor() {
        this.projects = [];
        this.proposals = [];
        this.init();
    }

    init() {
        // Check client access
        if (!authSystem.requireAuth('client')) {
            return;
        }

        this.loadUserInfo();
        this.loadDashboardData();
        this.setupEventListeners();
    }

    loadUserInfo() {
        const currentUser = authSystem.getCurrentUser();
        if (currentUser) {
            document.getElementById('clientName').textContent = currentUser.fullName;
            document.getElementById('welcomeName').textContent = currentUser.fullName;
        }
    }

    loadDashboardData() {
        this.loadProjects();
        this.loadProposals();
        this.loadRecommendedFreelancers();
        this.updateStats();
    }

    loadProjects() {
        // Load projects from localStorage (simulating database)
        const allProjects = JSON.parse(localStorage.getItem('portfolioPro_projects') || '[]');
        const currentUser = authSystem.getCurrentUser();
        this.projects = allProjects.filter(p => p.clientId === currentUser.id);
        
        this.displayActiveProjects();
    }

    displayActiveProjects() {
        const activeProjects = this.projects.filter(p => p.status === 'active' || p.status === 'in-progress');
        const container = document.getElementById('activeProjectsList');
        
        if (activeProjects.length === 0) {
            container.innerHTML = '<div class="no-data">No active projects. <a href="#" onclick="showPostProjectModal()">Post your first project!</a></div>';
            return;
        }
        
        container.innerHTML = activeProjects.map(project => `
            <div class="project-item">
                <div class="project-info">
                    <h4>${project.title}</h4>
                    <p>${project.description.substring(0, 100)}...</p>
                    <div class="project-meta">
                        <span class="project-status ${project.status}">${project.status}</span>
                        <span class="project-budget">Budget: ${project.budget}</span>
                        <span class="project-proposals">${project.proposalCount || 0} proposals</span>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="btn btn-sm btn-outline" onclick="clientDashboard.viewProject('${project.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                </div>
            </div>
        `).join('');
    }

    loadProposals() {
        // Load proposals for client's projects
        const allProposals = JSON.parse(localStorage.getItem('portfolioPro_proposals') || '[]');
        const projectIds = this.projects.map(p => p.id);
        this.proposals = allProposals.filter(p => projectIds.includes(p.projectId));
        
        this.displayRecentProposals();
    }

    displayRecentProposals() {
        const recentProposals = this.proposals.slice(0, 5);
        const container = document.getElementById('proposalsList');
        const countElement = document.getElementById('proposalCount');
        
        countElement.textContent = this.proposals.length;
        
        if (recentProposals.length === 0) {
            container.innerHTML = '<div class="no-data">No proposals yet</div>';
            return;
        }
        
        container.innerHTML = recentProposals.map(proposal => `
            <div class="proposal-item">
                <div class="proposal-info">
                    <h4>${proposal.freelancerName}</h4>
                    <p>Project: ${proposal.projectTitle}</p>
                    <div class="proposal-meta">
                        <span class="proposal-amount">$${proposal.amount}</span>
                        <span class="proposal-duration">${proposal.duration}</span>
                        <span class="proposal-date">${new Date(proposal.createdAt).toLocaleDateString()}</span>
                    </div>
                </div>
                <div class="proposal-actions">
                    <button class="btn btn-sm btn-primary" onclick="clientDashboard.acceptProposal('${proposal.id}')">
                        Accept
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="clientDashboard.viewProposal('${proposal.id}')">
                        View
                    </button>
                </div>
            </div>
        `).join('');
    }

    loadRecommendedFreelancers() {
        // Get active freelancers from users
        const users = authSystem.getUsersFromStorage();
        const freelancers = users.filter(u => u.role === 'freelancer' && u.status === 'active');
        
        const container = document.getElementById('recommendedFreelancers');
        
        if (freelancers.length === 0) {
            container.innerHTML = '<div class="no-data">No freelancers available</div>';
            return;
        }
        
        // Show top 3 freelancers
        const topFreelancers = freelancers.slice(0, 3);
        
        container.innerHTML = topFreelancers.map(freelancer => `
            <div class="freelancer-card-small">
                <img src="${freelancer.profileData.avatar}" alt="${freelancer.fullName}" class="freelancer-avatar-small">
                <div class="freelancer-info-small">
                    <h4>${freelancer.fullName}</h4>
                    <p>Full Stack Developer</p>
                    <div class="freelancer-rating-small">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span>4.9</span>
                    </div>
                </div>
                <button class="btn btn-sm btn-primary" onclick="clientDashboard.contactFreelancer('${freelancer.id}')">
                    Contact
                </button>
            </div>
        `).join('');
    }

    updateStats() {
        const activeProjects = this.projects.filter(p => p.status === 'active' || p.status === 'in-progress').length;
        const completedProjects = this.projects.filter(p => p.status === 'completed').length;
        const totalSpent = this.projects.reduce((sum, p) => sum + (p.paidAmount || 0), 0);
        
        document.getElementById('activeProjects').textContent = activeProjects;
        document.getElementById('completedProjects').textContent = completedProjects;
        document.getElementById('totalSpent').textContent = `$${totalSpent.toLocaleString()}`;
        document.getElementById('hiredFreelancers').textContent = this.projects.filter(p => p.freelancerId).length;
    }

    showPostProjectModal() {
        const modal = document.getElementById('postProjectModal');
        modal.style.display = 'block';
    }

    async postProject(formData) {
        const currentUser = authSystem.getCurrentUser();
        const project = {
            id: 'project_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            clientId: currentUser.id,
            clientName: currentUser.fullName,
            title: formData.get('title'),
            description: formData.get('description'),
            budget: formData.get('budget'),
            duration: formData.get('duration'),
            skills: formData.get('skills').split(',').map(s => s.trim()).filter(s => s),
            category: formData.get('category'),
            status: 'active',
            proposalCount: 0,
            createdAt: new Date().toISOString()
        };
        
        // Save to localStorage (simulating database)
        const allProjects = JSON.parse(localStorage.getItem('portfolioPro_projects') || '[]');
        allProjects.push(project);
        localStorage.setItem('portfolioPro_projects', JSON.stringify(allProjects));
        
        this.projects.push(project);
        this.displayActiveProjects();
        this.updateStats();
        
        this.showNotification('Project posted successfully!', 'success');
        this.closeModal('postProjectModal');
    }

    showBrowseFreelancers() {
        const modal = document.getElementById('browseFreelancersModal');
        modal.style.display = 'block';
        this.loadFreelancersList();
    }

    loadFreelancersList() {
        const users = authSystem.getUsersFromStorage();
        const freelancers = users.filter(u => u.role === 'freelancer' && u.status === 'active');
        
        const container = document.getElementById('freelancersList');
        
        if (freelancers.length === 0) {
            container.innerHTML = '<div class="no-data">No freelancers available</div>';
            return;
        }
        
        container.innerHTML = freelancers.map(freelancer => `
            <div class="freelancer-card">
                <div class="freelancer-avatar">
                    <img src="${freelancer.profileData.avatar}" alt="${freelancer.fullName}">
                </div>
                <div class="freelancer-info">
                    <h3>${freelancer.fullName}</h3>
                    <p class="freelancer-title">Full Stack Developer</p>
                    <div class="freelancer-rating">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <span>4.9 (25 reviews)</span>
                    </div>
                    <div class="freelancer-skills">
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Node.js</span>
                        <span class="skill-tag">MongoDB</span>
                    </div>
                    <div class="freelancer-rate">
                        <span>$50/hour</span>
                    </div>
                </div>
                <div class="freelancer-actions">
                    <button class="btn btn-primary" onclick="clientDashboard.contactFreelancer('${freelancer.id}')">
                        Contact
                    </button>
                    <button class="btn btn-outline" onclick="clientDashboard.viewFreelancerProfile('${freelancer.id}')">
                        View Profile
                    </button>
                </div>
            </div>
        `).join('');
    }

    viewProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (project) {
            alert(`Project Details:\n\nTitle: ${project.title}\nDescription: ${project.description}\nBudget: ${project.budget}\nStatus: ${project.status}\nProposals: ${project.proposalCount || 0}`);
        }
    }

    acceptProposal(proposalId) {
        if (!confirm('Are you sure you want to accept this proposal?')) {
            return;
        }
        
        // Update proposal status
        const allProposals = JSON.parse(localStorage.getItem('portfolioPro_proposals') || '[]');
        const proposalIndex = allProposals.findIndex(p => p.id === proposalId);
        
        if (proposalIndex !== -1) {
            allProposals[proposalIndex].status = 'accepted';
            localStorage.setItem('portfolioPro_proposals', JSON.stringify(allProposals));
            
            this.showNotification('Proposal accepted! The freelancer has been notified.', 'success');
            this.loadProposals();
        }
    }

    viewProposal(proposalId) {
        const proposal = this.proposals.find(p => p.id === proposalId);
        if (proposal) {
            alert(`Proposal Details:\n\nFreelancer: ${proposal.freelancerName}\nAmount: $${proposal.amount}\nDuration: ${proposal.duration}\nMessage: ${proposal.message}`);
        }
    }

    contactFreelancer(freelancerId) {
        alert('Messaging functionality coming soon!');
    }

    viewFreelancerProfile(freelancerId) {
        alert('Freelancer profile view coming soon!');
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.style.display = 'none';
    }

    setupEventListeners() {
        // Post project form
        const postProjectForm = document.getElementById('postProjectForm');
        if (postProjectForm) {
            postProjectForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(postProjectForm);
                
                const submitBtn = postProjectForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Posting...';
                submitBtn.disabled = true;
                
                try {
                    await this.postProject(formData);
                    postProjectForm.reset();
                } catch (error) {
                    this.showNotification('Failed to post project. Please try again.', 'error');
                } finally {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }
            });
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // User menu dropdown
        const userMenu = document.querySelector('.user-menu');
        if (userMenu) {
            userMenu.addEventListener('click', () => {
                const dropdown = userMenu.querySelector('.dropdown-menu');
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Global functions for HTML onclick handlers
function showPostProjectModal() {
    clientDashboard.showPostProjectModal();
}

function showBrowseFreelancers() {
    clientDashboard.showBrowseFreelancers();
}

function closeModal(modalId) {
    clientDashboard.closeModal(modalId);
}

// Initialize client dashboard
const clientDashboard = new ClientDashboard();
