// Authentication System for 3-User Role System
// Handles login, registration, session management, and role-based access control

class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.sessionKey = 'portfolioPro_session';
        this.init();
    }

    init() {
        // Create default admin user if none exists
        this.createDefaultAdmin();

        // Check for existing session on page load
        this.loadSession();
        this.setupEventListeners();
    }

    createDefaultAdmin() {
        const users = this.getUsersFromStorage();
        const adminExists = users.some(user => user.role === 'admin');

        if (!adminExists) {
            const defaultAdmin = {
                id: 'admin_default_001',
                email: '<EMAIL>',
                password: 'admin123', // In production, this should be hashed
                fullName: 'System Administrator',
                role: 'admin',
                status: 'active',
                createdAt: new Date().toISOString(),
                profileData: {
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face',
                    location: 'System',
                    bio: 'Platform Administrator',
                    joinDate: new Date().toISOString()
                }
            };

            users.push(defaultAdmin);
            localStorage.setItem('portfolioPro_users', JSON.stringify(users));

            console.log('Default admin created: <EMAIL> / admin123');
        }
    }

    // Session Management
    loadSession() {
        const sessionData = localStorage.getItem(this.sessionKey);
        if (sessionData) {
            try {
                this.currentUser = JSON.parse(sessionData);

                // Only redirect if we're on the index page or login page
                const currentPage = window.location.pathname.split('/').pop();
                if (currentPage === 'index.html' || currentPage === '' || currentPage === '/') {
                    this.redirectToDashboard();
                }
            } catch (error) {
                console.error('Invalid session data:', error);
                this.clearSession();
            }
        }
    }

    saveSession(userData) {
        localStorage.setItem(this.sessionKey, JSON.stringify(userData));
        this.currentUser = userData;
    }

    clearSession() {
        localStorage.removeItem(this.sessionKey);
        this.currentUser = null;
    }

    // User Registration
    async register(formData) {
        const userData = {
            id: this.generateUserId(),
            fullName: formData.get('fullName'),
            email: formData.get('email'),
            password: formData.get('password'), // In real app, this would be hashed
            role: formData.get('role'),
            status: formData.get('role') === 'freelancer' ? 'pending' : 'active',
            createdAt: new Date().toISOString(),
            profileData: this.getDefaultProfileData(formData.get('role'))
        };

        // Simulate API call
        return new Promise((resolve) => {
            setTimeout(() => {
                // Save user to localStorage (simulating database)
                this.saveUserToStorage(userData);
                
                // Create session
                this.saveSession(userData);
                
                resolve({
                    success: true,
                    user: userData,
                    message: userData.role === 'freelancer' 
                        ? 'Registration successful! Your account is pending approval.' 
                        : 'Registration successful!'
                });
            }, 1000);
        });
    }

    // User Login
    async login(email, password) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const users = this.getUsersFromStorage();
                const user = users.find(u => u.email === email && u.password === password);
                
                if (user) {
                    if (user.status === 'suspended') {
                        resolve({
                            success: false,
                            message: 'Your account has been suspended. Please contact support.'
                        });
                        return;
                    }
                    
                    if (user.role === 'freelancer' && user.status === 'pending') {
                        resolve({
                            success: false,
                            message: 'Your freelancer account is pending approval.'
                        });
                        return;
                    }
                    
                    this.saveSession(user);
                    resolve({
                        success: true,
                        user: user,
                        message: 'Login successful!'
                    });
                } else {
                    resolve({
                        success: false,
                        message: 'Invalid email or password.'
                    });
                }
            }, 800);
        });
    }

    // Logout
    logout() {
        this.clearSession();
        window.location.href = 'index.html';
    }

    // Role-based Dashboard Redirect
    redirectToDashboard() {
        if (!this.currentUser) {
            console.log('No current user for redirect');
            return;
        }

        const currentPage = window.location.pathname.split('/').pop();
        let targetPage;

        console.log('=== REDIRECT DEBUG ===');
        console.log('User role:', this.currentUser.role);
        console.log('Current page:', currentPage);
        console.log('Full user object:', this.currentUser);

        switch (this.currentUser.role) {
            case 'admin':
                targetPage = 'admin-dashboard.html';
                console.log('Admin user - targeting admin dashboard');
                break;
            case 'freelancer':
                targetPage = 'freelancer-dashboard.html';
                console.log('Freelancer user - targeting freelancer dashboard');
                break;
            case 'client':
                targetPage = 'client-dashboard.html';
                console.log('Client user - targeting client dashboard');
                break;
            default:
                targetPage = 'index.html';
                console.log('Unknown role - targeting index');
        }

        console.log('Target page determined:', targetPage);

        // Only redirect if not already on the correct page
        const specialPages = ['index.html', 'auth-test.html', 'test-auth-flow.html'];
        if (currentPage !== targetPage && !specialPages.includes(currentPage)) {
            console.log('REDIRECTING from', currentPage, 'to', targetPage);
            window.location.href = targetPage;
        } else {
            console.log('NOT redirecting - already on correct page or special page');
        }
        console.log('=== END REDIRECT DEBUG ===');
    }

    // Access Control
    hasPermission(requiredRole) {
        if (!this.currentUser) return false;
        
        const roleHierarchy = {
            'admin': 3,
            'freelancer': 2,
            'client': 1
        };
        
        return roleHierarchy[this.currentUser.role] >= roleHierarchy[requiredRole];
    }

    requireAuth(requiredRole = null) {
        console.log('=== REQUIRE AUTH DEBUG ===');
        console.log('RequireAuth called with role:', requiredRole);
        console.log('Current user:', this.currentUser);
        console.log('Current page:', window.location.pathname.split('/').pop());

        if (!this.currentUser) {
            console.log('No current user, redirecting to index');
            window.location.href = 'index.html';
            return false;
        }

        if (requiredRole && !this.hasPermission(requiredRole)) {
            console.log('Permission denied for role:', requiredRole, 'User role:', this.currentUser.role);
            console.log('Role hierarchy check failed - redirecting to appropriate dashboard');
            alert(`Access denied. You need ${requiredRole} permissions. Your role: ${this.currentUser.role}`);
            this.redirectToDashboard();
            return false;
        }

        console.log('Auth check passed for role:', requiredRole);
        console.log('=== END REQUIRE AUTH DEBUG ===');
        return true;
    }

    // Utility Methods
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getDefaultProfileData(role) {
        const baseProfile = {
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=120&h=120&fit=crop&crop=face',
            location: '',
            bio: '',
            joinDate: new Date().toISOString()
        };

        switch (role) {
            case 'freelancer':
                return {
                    ...baseProfile,
                    skills: [],
                    hourlyRate: 0,
                    portfolio: [],
                    rating: 0,
                    completedProjects: 0
                };
            case 'client':
                return {
                    ...baseProfile,
                    company: '',
                    projectsPosted: 0,
                    totalSpent: 0
                };
            case 'admin':
                return {
                    ...baseProfile,
                    permissions: ['all']
                };
            default:
                return baseProfile;
        }
    }

    saveUserToStorage(userData) {
        const users = this.getUsersFromStorage();
        users.push(userData);
        localStorage.setItem('portfolioPro_users', JSON.stringify(users));
    }

    getUsersFromStorage() {
        const users = localStorage.getItem('portfolioPro_users');
        return users ? JSON.parse(users) : [];
    }

    // Event Listeners
    setupEventListeners() {
        // Login form
        const loginForm = document.querySelector('#loginForm form');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(loginForm);
                const email = formData.get('email');
                const password = formData.get('password');
                
                const loginBtn = loginForm.querySelector('button[type="submit"]');
                const originalText = loginBtn.textContent;
                loginBtn.textContent = 'Signing In...';
                loginBtn.disabled = true;
                
                try {
                    const result = await this.login(email, password);
                    if (result.success) {
                        this.showMessage(result.message, 'success');
                        setTimeout(() => {
                            this.redirectToDashboard();
                        }, 1000);
                    } else {
                        this.showMessage(result.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('Login failed. Please try again.', 'error');
                } finally {
                    loginBtn.textContent = originalText;
                    loginBtn.disabled = false;
                }
            });
        }

        // Register form
        const registerForm = document.querySelector('#registerForm form');
        if (registerForm) {
            registerForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(registerForm);
                
                const registerBtn = registerForm.querySelector('button[type="submit"]');
                const originalText = registerBtn.textContent;
                registerBtn.textContent = 'Creating Account...';
                registerBtn.disabled = true;
                
                try {
                    const result = await this.register(formData);
                    if (result.success) {
                        this.showMessage(result.message, 'success');
                        setTimeout(() => {
                            this.redirectToDashboard();
                        }, 1500);
                    } else {
                        this.showMessage(result.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('Registration failed. Please try again.', 'error');
                } finally {
                    registerBtn.textContent = originalText;
                    registerBtn.disabled = false;
                }
            });
        }

        // Logout buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.logout-btn') || e.target.closest('.logout-btn')) {
                e.preventDefault();
                this.logout();
            }
        });
    }

    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.auth-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'auth-message';
            const authModal = document.querySelector('#authModal .modal-content');
            if (authModal) {
                authModal.insertBefore(messageEl, authModal.firstChild);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `auth-message ${type}`;
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    // Public API
    getCurrentUser() {
        return this.currentUser;
    }

    isLoggedIn() {
        return !!this.currentUser;
    }

    getUserRole() {
        return this.currentUser ? this.currentUser.role : null;
    }

    // Logout function
    logout() {
        this.clearSession();
        window.location.href = 'index.html';
    }
}

// Initialize authentication system
const authSystem = new AuthSystem();

// Export for global use
window.authSystem = authSystem;
