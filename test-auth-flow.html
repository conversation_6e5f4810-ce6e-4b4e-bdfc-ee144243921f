<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #2563eb; }
        .btn.success { background: #22c55e; }
        .btn.danger { background: #ef4444; }
        .btn.warning { background: #f59e0b; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #dcfce7; border: 1px solid #22c55e; color: #166534; }
        .status.error { background: #fef2f2; border: 1px solid #ef4444; color: #dc2626; }
        .status.info { background: #f0f9ff; border: 1px solid #0ea5e9; color: #0c4a6e; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; }
        .card { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔐 Authentication Flow Test</h1>
    
    <div class="test-section">
        <h2>Current Status</h2>
        <div id="currentStatus" class="status info">Loading...</div>
        <div id="userInfo"></div>
    </div>

    <div class="test-section">
        <h2>Quick Actions</h2>
        <div class="grid">
            <div class="card">
                <h3>Admin Test</h3>
                <button class="btn" onclick="loginAsAdmin()">Login as Admin</button>
                <button class="btn warning" onclick="testAdminAccess()">Test Admin Access</button>
            </div>
            <div class="card">
                <h3>Client Test</h3>
                <button class="btn" onclick="createAndLoginClient()">Create & Login Client</button>
                <button class="btn warning" onclick="testClientAccess()">Test Client Access</button>
            </div>
            <div class="card">
                <h3>Freelancer Test</h3>
                <button class="btn" onclick="createAndLoginFreelancer()">Create & Login Freelancer</button>
                <button class="btn warning" onclick="testFreelancerAccess()">Test Freelancer Access</button>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Navigation Tests</h2>
        <button class="btn" onclick="goToPage('admin-dashboard.html')">Go to Admin Dashboard</button>
        <button class="btn" onclick="goToPage('client-dashboard.html')">Go to Client Dashboard</button>
        <button class="btn" onclick="goToPage('dashboard.html')">Go to Freelancer Dashboard</button>
        <button class="btn danger" onclick="logout()">Logout</button>
    </div>

    <div class="test-section">
        <h2>System Information</h2>
        <button class="btn" onclick="showAllUsers()">Show All Users</button>
        <button class="btn" onclick="showSession()">Show Session</button>
        <button class="btn" onclick="testPermissions()">Test All Permissions</button>
        <button class="btn danger" onclick="clearAllData()">Clear All Data</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults">
            <div class="status info">Run tests above to see results...</div>
        </div>
    </div>

    <script src="scripts/auth.js"></script>
    <script>
        let testResults = [];

        function updateStatus() {
            const currentUser = authSystem.getCurrentUser();
            const statusDiv = document.getElementById('currentStatus');
            const userInfoDiv = document.getElementById('userInfo');
            
            if (currentUser) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `✅ Logged in as: <strong>${currentUser.fullName}</strong> (${currentUser.role})`;
                userInfoDiv.innerHTML = `
                    <pre>${JSON.stringify(currentUser, null, 2)}</pre>
                `;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Not logged in';
                userInfoDiv.innerHTML = '';
            }
        }

        function addTestResult(test, result, details = '') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({
                timestamp,
                test,
                result,
                details
            });
            
            const resultsDiv = document.getElementById('testResults');
            const resultClass = result === 'PASS' ? 'success' : 'error';
            const icon = result === 'PASS' ? '✅' : '❌';
            
            resultsDiv.innerHTML = `
                <div class="status ${resultClass}">
                    ${icon} [${timestamp}] <strong>${test}</strong>: ${result}
                    ${details ? `<br><small>${details}</small>` : ''}
                </div>
            ` + resultsDiv.innerHTML;
        }

        async function loginAsAdmin() {
            try {
                const result = await authSystem.login('<EMAIL>', 'admin123');
                if (result.success) {
                    addTestResult('Admin Login', 'PASS', 'Successfully logged in as admin');
                    updateStatus();
                } else {
                    addTestResult('Admin Login', 'FAIL', result.message);
                }
            } catch (error) {
                addTestResult('Admin Login', 'FAIL', error.message);
            }
        }

        async function createAndLoginClient() {
            try {
                const clientData = {
                    email: '<EMAIL>',
                    password: 'test123',
                    fullName: 'Test Client User',
                    role: 'client'
                };
                
                const result = await authSystem.register(clientData);
                if (result.success) {
                    addTestResult('Client Creation & Login', 'PASS', 'Client created and logged in');
                    updateStatus();
                } else {
                    addTestResult('Client Creation & Login', 'FAIL', result.message);
                }
            } catch (error) {
                addTestResult('Client Creation & Login', 'FAIL', error.message);
            }
        }

        async function createAndLoginFreelancer() {
            try {
                const freelancerData = {
                    email: '<EMAIL>',
                    password: 'test123',
                    fullName: 'Test Freelancer User',
                    role: 'freelancer'
                };
                
                const result = await authSystem.register(freelancerData);
                if (result.success) {
                    addTestResult('Freelancer Creation & Login', 'PASS', 'Freelancer created and logged in');
                    updateStatus();
                } else {
                    addTestResult('Freelancer Creation & Login', 'FAIL', result.message);
                }
            } catch (error) {
                addTestResult('Freelancer Creation & Login', 'FAIL', error.message);
            }
        }

        function testAdminAccess() {
            const hasAccess = authSystem.hasPermission('admin');
            const currentUser = authSystem.getCurrentUser();
            const userRole = currentUser ? currentUser.role : 'none';
            
            if (hasAccess && userRole === 'admin') {
                addTestResult('Admin Access Test', 'PASS', `User role: ${userRole}, Has admin permission: ${hasAccess}`);
            } else {
                addTestResult('Admin Access Test', 'FAIL', `User role: ${userRole}, Has admin permission: ${hasAccess}`);
            }
        }

        function testClientAccess() {
            const hasAccess = authSystem.hasPermission('client');
            const currentUser = authSystem.getCurrentUser();
            const userRole = currentUser ? currentUser.role : 'none';
            
            if (hasAccess && ['client', 'freelancer', 'admin'].includes(userRole)) {
                addTestResult('Client Access Test', 'PASS', `User role: ${userRole}, Has client permission: ${hasAccess}`);
            } else {
                addTestResult('Client Access Test', 'FAIL', `User role: ${userRole}, Has client permission: ${hasAccess}`);
            }
        }

        function testFreelancerAccess() {
            const hasAccess = authSystem.hasPermission('freelancer');
            const currentUser = authSystem.getCurrentUser();
            const userRole = currentUser ? currentUser.role : 'none';
            
            if (hasAccess && ['freelancer', 'admin'].includes(userRole)) {
                addTestResult('Freelancer Access Test', 'PASS', `User role: ${userRole}, Has freelancer permission: ${hasAccess}`);
            } else {
                addTestResult('Freelancer Access Test', 'FAIL', `User role: ${userRole}, Has freelancer permission: ${hasAccess}`);
            }
        }

        function goToPage(page) {
            addTestResult('Navigation Test', 'INFO', `Attempting to navigate to ${page}`);
            window.location.href = page;
        }

        function logout() {
            authSystem.logout();
            addTestResult('Logout Test', 'PASS', 'User logged out');
            updateStatus();
        }

        function showAllUsers() {
            const users = authSystem.getUsersFromStorage();
            addTestResult('Show Users', 'INFO', `Found ${users.length} users in storage`);
            console.log('All users:', users);
        }

        function showSession() {
            const session = localStorage.getItem('portfolioPro_session');
            addTestResult('Show Session', 'INFO', session ? 'Session exists' : 'No session found');
            console.log('Current session:', session);
        }

        function testPermissions() {
            const currentUser = authSystem.getCurrentUser();
            if (!currentUser) {
                addTestResult('Permission Test', 'FAIL', 'No user logged in');
                return;
            }
            
            const tests = [
                { role: 'admin', result: authSystem.hasPermission('admin') },
                { role: 'freelancer', result: authSystem.hasPermission('freelancer') },
                { role: 'client', result: authSystem.hasPermission('client') }
            ];
            
            let details = `User role: ${currentUser.role}\n`;
            tests.forEach(test => {
                details += `${test.role}: ${test.result ? 'ALLOWED' : 'DENIED'}\n`;
            });
            
            addTestResult('Permission Test', 'INFO', details);
        }

        function clearAllData() {
            if (confirm('This will clear all users and sessions. Continue?')) {
                localStorage.removeItem('portfolioPro_users');
                localStorage.removeItem('portfolioPro_session');
                authSystem.currentUser = null;
                authSystem.createDefaultAdmin();
                addTestResult('Clear Data', 'PASS', 'All data cleared, default admin recreated');
                updateStatus();
                testResults = [];
                document.getElementById('testResults').innerHTML = '<div class="status info">Test results cleared...</div>';
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            addTestResult('System Initialize', 'PASS', 'Authentication system loaded');
        });
    </script>
</body>
</html>
