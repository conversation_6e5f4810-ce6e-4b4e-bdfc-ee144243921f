/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Root Variables - Clean Professional Palette */
:root {
  /* Professional Color Palette */
  --primary-50: #9ebddd;
  --primary-100: #8bb0d4;
  --primary-200: #a1bcdf;
  --primary-300: #a0bde0;
  --primary-400: #94a3b8;
  --primary-500: #64748b;
  --primary-600: #475569;
  --primary-700: #334155;
  --primary-800: #1e293b;
  --primary-900: #0f172a;

  --accent-50: #748dad;
  --accent-100: #677991;
  --accent-200: #8194ac;
  --accent-300: #93c5fd;
  --accent-400: #60a5fa;
  --accent-500: #3b82f6;
  --accent-600: #2563eb;
  --accent-700: #1d4ed8;
  --accent-800: #1e40af;
  --accent-900: #1e3a8a;

  --success-500: #22c55e;
  --success-600: #16a34a;
  --warning-500: #f59e0b;
  --error-500: #ef4444;

  /* Clean Gradients */
  --gradient-primary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);

  /* Professional Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Clean Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(0, 0, 0, 0.1);
  --glass-backdrop: blur(20px);
}

/* Dark Modern Body Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #f8fafc;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  overflow-x: hidden;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-gradient {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.nav-brand h2 {
  font-size: 1.5rem;
  margin: 0;
}

.nav-menu {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: #f8fafc;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--accent-400);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  color: #f8fafc;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Hero Section */
.hero {
  padding: 8rem 0 4rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-description {
  font-size: 1.25rem;
  color: #cbd5e1;
  margin-bottom: 2rem;
}

.hero-search {
  margin-bottom: 2rem;
}

.search-container {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50px;
  padding: 0.5rem;
  box-shadow: var(--shadow-xl);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  background: transparent;
  color: #1e293b;
}

.search-btn {
  background: var(--accent-600);
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: var(--accent-700);
  transform: scale(1.05);
}

.hero-tags {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-size: 0.875rem;
  color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-2xl);
}

.card-icon {
  font-size: 3rem;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--accent-600);
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  background: var(--accent-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
  text-decoration: none;
}

.btn-outline {
  background: transparent;
  color: var(--accent-600);
  border: 2px solid var(--accent-600);
}

.btn-outline:hover {
  background: var(--accent-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

/* Section Styles */
.py-20 {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #cbd5e1;
}

.text-center {
  text-align: center;
}

.mb-12 {
  margin-bottom: 3rem;
}

/* Services Section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.service-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(255, 255, 255, 0.2);
}

.service-icon {
  font-size: 3rem;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.service-card p {
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-weight: 600;
  color: var(--accent-400);
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #fbbf24;
}

/* Freelancers Section */
.freelancers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.freelancer-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.freelancer-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.freelancer-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
}

.freelancer-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  background: var(--success-500);
  border: 2px solid white;
  border-radius: 50%;
}

.freelancer-info {
  text-align: center;
}

.freelancer-info h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.freelancer-title {
  color: #cbd5e1;
  margin-bottom: 1rem;
}

.freelancer-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  display: flex;
  gap: 0.125rem;
  color: #fbbf24;
}

.freelancer-skills {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.skill-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
  border-radius: 12px;
  font-size: 0.75rem;
}

.freelancer-price {
  font-weight: 600;
  color: var(--accent-400);
}

/* Glass Morphism Components */
.glass-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

/* Testimonials Section */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.quote-icon {
  font-size: 2rem;
  color: var(--accent-600);
  margin-bottom: 1rem;
}

.testimonial-content p {
  color: #475569;
  font-style: italic;
  font-size: 1.125rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.author-info p {
  color: #64748b;
  font-size: 0.875rem;
}

/* Footer */
.footer {
  background: var(--primary-900);
  padding: 4rem 0 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--accent-400);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #f8fafc;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--accent-600);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #cbd5e1;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  margin: 5% auto;
  padding: 2rem;
  border-radius: 24px;
  width: 90%;
  max-width: 400px;
  position: relative;
  box-shadow: var(--shadow-2xl);
}

.close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  font-size: 2rem;
  cursor: pointer;
  color: #64748b;
}

.close:hover {
  color: #1e293b;
}

.auth-form h2 {
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-600);
}

.w-full {
  width: 100%;
}

.auth-switch {
  text-align: center;
  margin-top: 1rem;
  color: #64748b;
}

.auth-switch a {
  color: var(--accent-600);
  text-decoration: none;
}

.auth-switch a:hover {
  text-decoration: underline;
}

/* Auth Messages */
.auth-message {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.auth-message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.auth-message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.auth-message.info {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

/* Form Improvements */
.form-note {
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
  margin-top: 0.5rem;
}

.form-input:invalid {
  border-color: #ef4444;
}

.form-input:valid {
  border-color: #10b981;
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Role Selection Styling */
select.form-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Admin Dashboard Styles */
.admin-stat {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.badge {
  background: var(--accent-600);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Approval Items */
.approval-list {
  max-height: 400px;
  overflow-y: auto;
}

.approval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.approval-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.approval-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.approval-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.approval-info p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.approval-date {
  font-size: 0.8rem;
  color: #9ca3af;
}

.approval-actions {
  display: flex;
  gap: 0.5rem;
}

/* Activity Items */
.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-600);
  flex-shrink: 0;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  color: #374151;
  font-size: 0.9rem;
}

.activity-time {
  font-size: 0.8rem;
  color: #9ca3af;
}

/* System Health */
.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
}

.metric-label {
  font-weight: 500;
  color: #374151;
}

.metric-value {
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.metric-value.online {
  background: #d1fae5;
  color: #065f46;
}

.metric-value.offline {
  background: #fee2e2;
  color: #991b1b;
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.offline {
  background: #ef4444;
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  color: #374151;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.quick-action-btn:hover {
  border-color: var(--accent-600);
  background: var(--accent-50);
  color: var(--accent-600);
  transform: translateY(-2px);
}

.quick-action-btn i {
  font-size: 1.5rem;
}

.quick-action-btn span {
  font-weight: 500;
  font-size: 0.9rem;
}

/* User Management Modal */
.large-modal {
  max-width: 800px;
  width: 90%;
}

.user-management-content {
  margin-top: 1rem;
}

.user-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.user-filters select {
  flex: 1;
}

.users-list {
  max-height: 500px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.user-details p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.user-meta {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.user-role, .user-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.user-role.admin {
  background: #fef3c7;
  color: #92400e;
}

.user-role.freelancer {
  background: #dbeafe;
  color: #1e40af;
}

.user-role.client {
  background: #d1fae5;
  color: #065f46;
}

.user-status.active {
  background: #d1fae5;
  color: #065f46;
}

.user-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.user-status.suspended {
  background: #fee2e2;
  color: #991b1b;
}

.user-date {
  font-size: 0.8rem;
  color: #9ca3af;
}

.user-actions {
  display: flex;
  gap: 0.5rem;
}

/* Dropdown Menu */
.user-menu {
  position: relative;
  cursor: pointer;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  min-width: 150px;
  z-index: 1000;
  display: none;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #f9fafb;
}

/* Notifications */
.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1000;
  min-width: 300px;
  animation: slideInRight 0.3s ease;
}

.notification.success {
  border-left: 4px solid #10b981;
}

.notification.warning {
  border-left: 4px solid #f59e0b;
}

.notification.error {
  border-left: 4px solid #ef4444;
}

.notification.info {
  border-left: 4px solid #3b82f6;
}

.notification i {
  font-size: 1.2rem;
}

.notification.success i {
  color: #10b981;
}

.notification.warning i {
  color: #f59e0b;
}

.notification.error i {
  color: #ef4444;
}

.notification.info i {
  color: #3b82f6;
}

.notification-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: auto;
}

.notification-close:hover {
  color: #374151;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* No Data State */
.no-data {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  padding: 2rem;
}

/* Client Dashboard Styles */
.client-stat {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Project Items */
.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.project-info h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.project-info p {
  margin: 0 0 0.75rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.project-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.project-status.active {
  background: #dbeafe;
  color: #1e40af;
}

.project-status.in-progress {
  background: #fef3c7;
  color: #92400e;
}

.project-status.completed {
  background: #d1fae5;
  color: #065f46;
}

.project-budget, .project-proposals {
  font-size: 0.8rem;
  color: #6b7280;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
}

/* Proposal Items */
.proposal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.proposal-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.proposal-info p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.proposal-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.proposal-amount {
  font-weight: 600;
  color: #059669;
}

.proposal-duration, .proposal-date {
  font-size: 0.8rem;
  color: #6b7280;
}

.proposal-actions {
  display: flex;
  gap: 0.5rem;
}

/* Freelancer Recommendations */
.freelancer-recommendations {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.freelancer-card-small {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.freelancer-avatar-small {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.freelancer-info-small {
  flex: 1;
}

.freelancer-info-small h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.freelancer-info-small p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.freelancer-rating-small {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.freelancer-rating-small .stars {
  color: #fbbf24;
  font-size: 0.8rem;
}

.freelancer-rating-small span {
  font-size: 0.8rem;
  color: #6b7280;
}

/* Form Improvements */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

textarea.form-input {
  resize: vertical;
  min-height: 100px;
}

/* Freelancer Filters */
.freelancer-filters {
  margin-bottom: 1.5rem;
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Freelancers Grid */
.freelancers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  max-height: 500px;
  overflow-y: auto;
}

.freelancer-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  transition: all 0.3s ease;
}

.freelancer-card:hover {
  border-color: var(--accent-600);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.freelancer-avatar {
  text-align: center;
  margin-bottom: 1rem;
}

.freelancer-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.freelancer-info {
  text-align: center;
}

.freelancer-info h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.freelancer-title {
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.freelancer-rating {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.freelancer-rating .stars {
  color: #fbbf24;
}

.freelancer-rating span {
  color: #6b7280;
  font-size: 0.9rem;
}

.freelancer-skills {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.skill-tag {
  background: var(--accent-100);
  color: var(--accent-600);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.freelancer-rate {
  text-align: center;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #059669;
}

.freelancer-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .freelancers-grid {
    grid-template-columns: 1fr;
  }

  .project-item, .proposal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .project-actions, .proposal-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Dashboard Styles */
.dashboard-main {
  padding-top: 6rem;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
}

.welcome-section h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  color: #cbd5e1;
  font-size: 1.125rem;
}

.quick-actions {
  display: flex;
  gap: 1rem;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: #f8fafc;
}

.nav-link.active {
  color: var(--accent-400);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-600);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-content h3 {
  font-size: 2rem;
  margin-bottom: 0.25rem;
}

.stat-content p {
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.stat-change {
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-500);
}

.stat-change.negative {
  color: var(--error-500);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.dashboard-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.dashboard-card.full-width {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
  font-size: 1.25rem;
  margin: 0;
}

.view-all {
  color: var(--accent-400);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-all:hover {
  color: var(--accent-300);
}

.card-body {
  padding: 1.5rem;
}

/* Project List */
.project-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.project-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.project-info h4 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.project-info p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.project-status.in-progress {
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
}

.project-status.completed {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-500);
}

.project-status.pending {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-500);
}

.project-deadline {
  font-size: 0.75rem;
  color: #94a3b8;
}

.project-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--accent-400);
}

/* Message List */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
}

.message-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-content h4 {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.message-content p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.message-status.unread {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 8px;
  height: 8px;
  background: var(--accent-500);
  border-radius: 50%;
}

/* Chart Styles */
.chart-controls {
  display: flex;
  gap: 0.5rem;
}

.chart-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #cbd5e1;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
  background: var(--accent-600);
  border-color: var(--accent-600);
  color: white;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 250px;
  padding: 1rem 0;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--accent-600), var(--accent-400));
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(to top, var(--accent-700), var(--accent-500));
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--accent-600);
  transform: translateY(-2px);
}

.quick-action-btn i {
  font-size: 1.5rem;
  color: var(--accent-400);
}

.quick-action-btn span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Review List */
.review-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review-item {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.review-rating {
  display: flex;
  gap: 0.125rem;
  color: #fbbf24;
}

.review-date {
  font-size: 0.75rem;
  color: #94a3b8;
}

.review-item p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  font-style: italic;
}

.review-author {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: block;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .services-grid,
  .freelancers-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .user-menu .user-name {
    display: none;
  }
}
