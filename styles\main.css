/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Root Variables - Clean Professional Palette */
:root {
  /* Professional Color Palette */
  --primary-50: #9ebddd;
  --primary-100: #8bb0d4;
  --primary-200: #a1bcdf;
  --primary-300: #a0bde0;
  --primary-400: #94a3b8;
  --primary-500: #64748b;
  --primary-600: #475569;
  --primary-700: #334155;
  --primary-800: #1e293b;
  --primary-900: #0f172a;

  --accent-50: #748dad;
  --accent-100: #677991;
  --accent-200: #8194ac;
  --accent-300: #93c5fd;
  --accent-400: #60a5fa;
  --accent-500: #3b82f6;
  --accent-600: #2563eb;
  --accent-700: #1d4ed8;
  --accent-800: #1e40af;
  --accent-900: #1e3a8a;

  --success-500: #22c55e;
  --success-600: #16a34a;
  --warning-500: #f59e0b;
  --error-500: #ef4444;

  /* Clean Gradients */
  --gradient-primary: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  --gradient-hero: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);

  /* Professional Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Clean Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(0, 0, 0, 0.1);
  --glass-backdrop: blur(20px);
}

/* Dark Modern Body Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #f8fafc;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  overflow-x: hidden;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.text-gradient {
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.nav-brand h2 {
  font-size: 1.5rem;
  margin: 0;
}

.nav-menu {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: #f8fafc;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--accent-400);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-toggle {
  display: none;
  background: none;
  border: none;
  color: #f8fafc;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Hero Section */
.hero {
  padding: 8rem 0 4rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-description {
  font-size: 1.25rem;
  color: #cbd5e1;
  margin-bottom: 2rem;
}

.hero-search {
  margin-bottom: 2rem;
}

.search-container {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50px;
  padding: 0.5rem;
  box-shadow: var(--shadow-xl);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  background: transparent;
  color: #1e293b;
}

.search-btn {
  background: var(--accent-600);
  border: none;
  border-radius: 50px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  background: var(--accent-700);
  transform: scale(1.05);
}

.hero-tags {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-size: 0.875rem;
  color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-2xl);
}

.card-icon {
  font-size: 3rem;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  border-radius: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--accent-600);
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  background: var(--accent-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
  text-decoration: none;
}

.btn-outline {
  background: transparent;
  color: var(--accent-600);
  border: 2px solid var(--accent-600);
}

.btn-outline:hover {
  background: var(--accent-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

/* Section Styles */
.py-20 {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.section-description {
  font-size: 1.125rem;
  color: #cbd5e1;
}

.text-center {
  text-align: center;
}

.mb-12 {
  margin-bottom: 3rem;
}

/* Services Section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.service-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(255, 255, 255, 0.2);
}

.service-icon {
  font-size: 3rem;
  color: var(--accent-400);
  margin-bottom: 1rem;
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.service-card p {
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-weight: 600;
  color: var(--accent-400);
}

.rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #fbbf24;
}

/* Freelancers Section */
.freelancers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
}

.freelancer-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.freelancer-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.freelancer-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
}

.freelancer-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.online-status {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  background: var(--success-500);
  border: 2px solid white;
  border-radius: 50%;
}

.freelancer-info {
  text-align: center;
}

.freelancer-info h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.freelancer-title {
  color: #cbd5e1;
  margin-bottom: 1rem;
}

.freelancer-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  display: flex;
  gap: 0.125rem;
  color: #fbbf24;
}

.freelancer-skills {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.skill-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
  border-radius: 12px;
  font-size: 0.75rem;
}

.freelancer-price {
  font-weight: 600;
  color: var(--accent-400);
}

/* Glass Morphism Components */
.glass-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

/* Testimonials Section */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.testimonial-content {
  margin-bottom: 1.5rem;
}

.quote-icon {
  font-size: 2rem;
  color: var(--accent-600);
  margin-bottom: 1rem;
}

.testimonial-content p {
  color: #475569;
  font-style: italic;
  font-size: 1.125rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.author-info p {
  color: #64748b;
  font-size: 0.875rem;
}

/* Footer */
.footer {
  background: var(--primary-900);
  padding: 4rem 0 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--accent-400);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #f8fafc;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--accent-600);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #cbd5e1;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  margin: 5% auto;
  padding: 2rem;
  border-radius: 24px;
  width: 90%;
  max-width: 400px;
  position: relative;
  box-shadow: var(--shadow-2xl);
}

.close {
  position: absolute;
  right: 1rem;
  top: 1rem;
  font-size: 2rem;
  cursor: pointer;
  color: #64748b;
}

.close:hover {
  color: #1e293b;
}

.auth-form h2 {
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-600);
}

.w-full {
  width: 100%;
}

.auth-switch {
  text-align: center;
  margin-top: 1rem;
  color: #64748b;
}

.auth-switch a {
  color: var(--accent-600);
  text-decoration: none;
}

.auth-switch a:hover {
  text-decoration: underline;
}

/* Auth Messages */
.auth-message {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.auth-message.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.auth-message.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
}

.auth-message.info {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

/* Form Improvements */
.form-note {
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
  margin-top: 0.5rem;
}

.form-input:invalid {
  border-color: #ef4444;
}

.form-input:valid {
  border-color: #10b981;
}

/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Role Selection Styling */
select.form-input {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Admin Dashboard Styles */
.admin-stat {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.badge {
  background: var(--accent-600);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Approval Items */
.approval-list {
  max-height: 400px;
  overflow-y: auto;
}

.approval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.approval-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.approval-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.approval-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.approval-info p {
  margin: 0 0 0.25rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.approval-date {
  font-size: 0.8rem;
  color: #9ca3af;
}

.approval-actions {
  display: flex;
  gap: 0.5rem;
}

/* Activity Items */
.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-600);
  flex-shrink: 0;
}

.activity-content p {
  margin: 0 0 0.25rem 0;
  color: #374151;
  font-size: 0.9rem;
}

.activity-time {
  font-size: 0.8rem;
  color: #9ca3af;
}

/* System Health */
.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
}

.metric-label {
  font-weight: 500;
  color: #374151;
}

.metric-value {
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.metric-value.online {
  background: #d1fae5;
  color: #065f46;
}

.metric-value.offline {
  background: #fee2e2;
  color: #991b1b;
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.online {
  background: #10b981;
}

.status-indicator.offline {
  background: #ef4444;
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  color: #374151;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.quick-action-btn:hover {
  border-color: var(--accent-600);
  background: var(--accent-50);
  color: var(--accent-600);
  transform: translateY(-2px);
}

.quick-action-btn i {
  font-size: 1.5rem;
}

.quick-action-btn span {
  font-weight: 500;
  font-size: 0.9rem;
}

/* User Management Modal */
.large-modal {
  max-width: 800px;
  width: 90%;
}

.user-management-content {
  margin-top: 1rem;
}

.user-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.user-filters select {
  flex: 1;
}

.users-list {
  max-height: 500px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.user-details p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.user-meta {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.user-role, .user-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.user-role.admin {
  background: #fef3c7;
  color: #92400e;
}

.user-role.freelancer {
  background: #dbeafe;
  color: #1e40af;
}

.user-role.client {
  background: #d1fae5;
  color: #065f46;
}

.user-status.active {
  background: #d1fae5;
  color: #065f46;
}

.user-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.user-status.suspended {
  background: #fee2e2;
  color: #991b1b;
}

.user-date {
  font-size: 0.8rem;
  color: #9ca3af;
}

.user-actions {
  display: flex;
  gap: 0.5rem;
}

/* Dropdown Menu */
.user-menu {
  position: relative;
  cursor: pointer;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  min-width: 150px;
  z-index: 1000;
  display: none;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #374151;
  text-decoration: none;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #f9fafb;
}

/* Notifications */
.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1000;
  min-width: 300px;
  animation: slideInRight 0.3s ease;
}

.notification.success {
  border-left: 4px solid #10b981;
}

.notification.warning {
  border-left: 4px solid #f59e0b;
}

.notification.error {
  border-left: 4px solid #ef4444;
}

.notification.info {
  border-left: 4px solid #3b82f6;
}

.notification i {
  font-size: 1.2rem;
}

.notification.success i {
  color: #10b981;
}

.notification.warning i {
  color: #f59e0b;
}

.notification.error i {
  color: #ef4444;
}

.notification.info i {
  color: #3b82f6;
}

.notification-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  margin-left: auto;
}

.notification-close:hover {
  color: #374151;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* No Data State */
.no-data {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  padding: 2rem;
}

/* Client Dashboard Styles */
.client-stat {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Project Items */
.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.project-info h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.project-info p {
  margin: 0 0 0.75rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.project-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.project-status.active {
  background: #dbeafe;
  color: #1e40af;
}

.project-status.in-progress {
  background: #fef3c7;
  color: #92400e;
}

.project-status.completed {
  background: #d1fae5;
  color: #065f46;
}

.project-budget, .project-proposals {
  font-size: 0.8rem;
  color: #6b7280;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
}

/* Proposal Items */
.proposal-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: white;
}

.proposal-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.proposal-info p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.proposal-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.proposal-amount {
  font-weight: 600;
  color: #059669;
}

.proposal-duration, .proposal-date {
  font-size: 0.8rem;
  color: #6b7280;
}

.proposal-actions {
  display: flex;
  gap: 0.5rem;
}

/* Freelancer Recommendations */
.freelancer-recommendations {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.freelancer-card-small {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.freelancer-avatar-small {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.freelancer-info-small {
  flex: 1;
}

.freelancer-info-small h4 {
  margin: 0 0 0.25rem 0;
  color: #1f2937;
}

.freelancer-info-small p {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.freelancer-rating-small {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.freelancer-rating-small .stars {
  color: #fbbf24;
  font-size: 0.8rem;
}

.freelancer-rating-small span {
  font-size: 0.8rem;
  color: #6b7280;
}

/* Form Improvements */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

textarea.form-input {
  resize: vertical;
  min-height: 100px;
}

/* Freelancer Filters */
.freelancer-filters {
  margin-bottom: 1.5rem;
}

.filter-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Freelancers Grid */
.freelancers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  max-height: 500px;
  overflow-y: auto;
}

.freelancer-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  transition: all 0.3s ease;
}

.freelancer-card:hover {
  border-color: var(--accent-600);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.freelancer-avatar {
  text-align: center;
  margin-bottom: 1rem;
}

.freelancer-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.freelancer-info {
  text-align: center;
}

.freelancer-info h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.freelancer-title {
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.freelancer-rating {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.freelancer-rating .stars {
  color: #fbbf24;
}

.freelancer-rating span {
  color: #6b7280;
  font-size: 0.9rem;
}

.freelancer-skills {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.skill-tag {
  background: var(--accent-100);
  color: var(--accent-600);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.freelancer-rate {
  text-align: center;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #059669;
}

.freelancer-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .freelancers-grid {
    grid-template-columns: 1fr;
  }

  .project-item, .proposal-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .project-actions, .proposal-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Dashboard Styles */
.dashboard-main {
  padding-top: 6rem;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 2rem 0;
}

.welcome-section h1 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.welcome-section p {
  color: #cbd5e1;
  font-size: 1.125rem;
}

.quick-actions {
  display: flex;
  gap: 1rem;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: #f8fafc;
}

.nav-link.active {
  color: var(--accent-400);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-600);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-content h3 {
  font-size: 2rem;
  margin-bottom: 0.25rem;
}

.stat-content p {
  color: #cbd5e1;
  margin-bottom: 0.5rem;
}

.stat-change {
  font-size: 0.875rem;
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-500);
}

.stat-change.negative {
  color: var(--error-500);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.dashboard-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  overflow: hidden;
}

.dashboard-card.full-width {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
  font-size: 1.25rem;
  margin: 0;
}

.view-all {
  color: var(--accent-400);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-all:hover {
  color: var(--accent-300);
}

.card-body {
  padding: 1.5rem;
}

/* Project List */
.project-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.project-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.project-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.project-info h4 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.project-info p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.project-status.in-progress {
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-300);
}

.project-status.completed {
  background: rgba(34, 197, 94, 0.2);
  color: var(--success-500);
}

.project-status.pending {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-500);
}

.project-deadline {
  font-size: 0.75rem;
  color: #94a3b8;
}

.project-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--accent-400);
}

/* Message List */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
}

.message-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-content h4 {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.message-content p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-time {
  font-size: 0.75rem;
  color: #94a3b8;
}

.message-status.unread {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 8px;
  height: 8px;
  background: var(--accent-500);
  border-radius: 50%;
}

/* Chart Styles */
.chart-controls {
  display: flex;
  gap: 0.5rem;
}

.chart-btn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #cbd5e1;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
  background: var(--accent-600);
  border-color: var(--accent-600);
  color: white;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 250px;
  padding: 1rem 0;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--accent-600), var(--accent-400));
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  background: linear-gradient(to top, var(--accent-700), var(--accent-500));
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--accent-600);
  transform: translateY(-2px);
}

.quick-action-btn i {
  font-size: 1.5rem;
  color: var(--accent-400);
}

.quick-action-btn span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Review List */
.review-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review-item {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.review-rating {
  display: flex;
  gap: 0.125rem;
  color: #fbbf24;
}

.review-date {
  font-size: 0.75rem;
  color: #94a3b8;
}

.review-item p {
  color: #cbd5e1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  font-style: italic;
}

.review-author {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Portfolio Management Styles */
.portfolio-section {
  padding: 40px 0;
  background: var(--bg-secondary);
}

.portfolio-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.project-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.project-images {
  height: 200px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.project-images img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-images .no-image {
  color: var(--text-secondary);
  font-size: 3rem;
}

.project-content {
  padding: 25px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.project-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.project-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: var(--bg-secondary);
  color: var(--primary);
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.project-category {
  background: var(--primary-light);
  color: var(--primary);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.project-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-completed {
  background: #dcfce7;
  color: #166534;
}

.status-in-progress {
  background: #fef3c7;
  color: #92400e;
}

.status-planned {
  background: #e0e7ff;
  color: #3730a3;
}

.project-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.skill-tag {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.project-links {
  display: flex;
  gap: 10px;
}

.project-links .btn {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.large-modal {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 5px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Form Styles */
.project-form {
  padding: 30px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.skills-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  padding: 15px;
  background: var(--bg-secondary);
  border-radius: 10px;
}

.skills-container .skill-tag {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.skills-container .skill-tag:hover {
  background: var(--primary-light);
}

.skills-container input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.file-upload-help {
  margin-top: 8px;
}

.file-upload-help small {
  color: var(--text-secondary);
}

.file-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.file-preview-item {
  background: var(--bg-secondary);
  padding: 15px;
  border-radius: 10px;
  text-align: center;
}

.file-preview-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 10px;
}

.file-info p {
  margin: 5px 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: var(--text-primary);
}

.empty-state p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  transform: translateX(400px);
  transition: all 0.3s ease;
  z-index: 1001;
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  border-left: 4px solid #22c55e;
}

.notification-error {
  border-left: 4px solid #ef4444;
}

.notification-info {
  border-left: 4px solid #3b82f6;
}

.notification i {
  font-size: 1.2rem;
}

.notification-success i {
  color: #22c55e;
}

.notification-error i {
  color: #ef4444;
}

.notification-info i {
  color: #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: block;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .services-grid,
  .freelancers-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .user-menu .user-name {
    display: none;
  }

  /* Portfolio Management Mobile Styles */
  .portfolio-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header {
    padding: 20px;
  }

  .project-form {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .skills-container {
    grid-template-columns: 1fr;
  }

  .file-preview {
    grid-template-columns: repeat(2, 1fr);
  }

  .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .project-actions {
    align-self: flex-end;
  }

  .project-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .project-links {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
  }

  .notification {
    right: 10px;
    left: 10px;
    transform: translateY(-100px);
  }

  .notification.show {
    transform: translateY(0);
  }

  /* Calendar Mobile Styles */
  .calendar-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .calendar-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .calendar-navigation {
    justify-content: center;
  }

  .calendar-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .calendar-date {
    min-height: 80px;
    padding: 0.25rem;
  }

  .date-events {
    max-height: 50px;
  }

  .calendar-event {
    font-size: 0.625rem;
    padding: 1px 4px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .event-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .event-time {
    min-width: auto;
  }
}

/* Calendar Styles */
.calendar-main {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.calendar-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  margin-top: 2rem;
}

.calendar-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.calendar-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-content,
.upcoming-events,
.calendar-stats {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Calendar Header */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.calendar-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.calendar-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  min-width: 200px;
  text-align: center;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.view-mode-buttons {
  display: flex;
  background: #f1f5f9;
  border-radius: 8px;
  padding: 4px;
}

.view-mode-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-mode-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Calendar Month View */
.calendar-month {
  width: 100%;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
}

.weekday {
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: #64748b;
  background: #f8fafc;
  font-size: 0.875rem;
}

.calendar-weeks {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.calendar-date {
  min-height: 120px;
  background: white;
  border: 1px solid #e2e8f0;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.calendar-date:hover {
  background: #f8fafc;
  border-color: #3b82f6;
}

.calendar-date.today {
  background: #eff6ff;
  border-color: #3b82f6;
}

.calendar-date.selected {
  background: #dbeafe;
  border-color: #2563eb;
  box-shadow: inset 0 0 0 2px #3b82f6;
}

.calendar-date.other-month {
  color: #94a3b8;
  background: #f8fafc;
}

.calendar-date.has-events .date-number {
  font-weight: 600;
  color: #1e293b;
}

.date-number {
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
  margin-bottom: 0.25rem;
}

.date-events {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-height: 80px;
  overflow: hidden;
}

.calendar-event {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  color: white;
  cursor: pointer;
  transition: opacity 0.2s ease;
  line-height: 1.2;
}

.calendar-event:hover {
  opacity: 0.8;
}

.event-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-events {
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
  padding: 2px;
}

/* Event Items */
.event-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  background: #f8fafc;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
}

.event-item:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.event-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 120px;
}

.event-details {
  flex: 1;
}

.event-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.event-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.event-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #64748b;
}

.event-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.btn-icon:hover {
  background: #e2e8f0;
  color: #1e293b;
}

/* Upcoming Events */
.upcoming-events h3,
.calendar-stats h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.upcoming-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.upcoming-event {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upcoming-event:hover {
  background: #f1f5f9;
  transform: translateX(2px);
}

.event-indicator {
  width: 4px;
  height: 40px;
  border-radius: 2px;
}

.event-content .event-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.event-content .event-time {
  font-size: 0.75rem;
  color: #64748b;
}

/* Calendar Stats */
.stats-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.meeting {
  background: #3b82f6;
}

.stat-icon.deadline {
  background: #ef4444;
}

.stat-icon.reminder {
  background: #f59e0b;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Event Form */
.event-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.large-modal .modal-content {
  max-width: 600px;
  width: 90vw;
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #64748b;
}

/* No Events State */
.no-events {
  text-align: center;
  padding: 2rem;
  color: #64748b;
  font-style: italic;
}

/* Export Styles */
.export-section {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.export-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.export-section p {
  color: #64748b;
  font-size: 1rem;
}

.export-options,
.export-settings {
  margin-bottom: 2rem;
}

.export-options h4,
.export-settings h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.format-grid,
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.format-option,
.template-option {
  cursor: pointer;
}

.format-option input,
.template-option input {
  display: none;
}

.format-card,
.template-card {
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  text-align: center;
  transition: all 0.2s ease;
  background: white;
}

.format-card:hover,
.template-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.format-option input:checked + .format-card,
.template-option input:checked + .template-card {
  border-color: #3b82f6;
  background: #eff6ff;
}

.format-card i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.format-card span,
.template-card span {
  display: block;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.format-card small {
  color: #64748b;
  font-size: 0.875rem;
}

.template-preview {
  width: 60px;
  height: 80px;
  margin: 0 auto 0.5rem;
  border-radius: 4px;
  background: #f1f5f9;
  position: relative;
}

.template-preview.modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.template-preview.classic {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.template-preview.creative {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: #f8fafc;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-item:hover {
  background: #f1f5f9;
}

.checkbox-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
}

.checkbox-item span {
  font-weight: 500;
  color: #1e293b;
}

.export-progress {
  text-align: center;
  padding: 3rem 2rem;
}

.progress-icon {
  font-size: 3rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.export-progress h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.export-progress p {
  color: #64748b;
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  width: 0%;
  transition: width 2s ease;
  border-radius: 4px;
}

/* Auth Message Styles */
.auth-message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
    animation: slideDown 0.3s ease-out;
}

.auth-message.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-600);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.auth-message.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-600);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.auth-message.info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-600);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.auth-message i {
    font-size: 1.25rem;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auth Form Enhancements */
.auth-links {
    text-align: center;
    margin-top: 1rem;
}

.auth-links p {
    margin: 0.5rem 0;
}

.forgot-password {
    font-size: 0.875rem;
}

.forgot-password a {
    color: var(--text-secondary);
    text-decoration: none;
}

.forgot-password a:hover {
    color: var(--accent-500);
    text-decoration: underline;
}

.auth-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Analytics Modal Styles */
.analytics-modal {
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  width: 95vw;
}

.analytics-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics-stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.analytics-stat-card:hover {
  border-color: var(--accent-400);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.analytics-stat-card .stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--accent-500), var(--accent-400));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.analytics-stat-card .stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.analytics-stat-card .stat-content p {
  margin: 0.25rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.analytics-stat-card .stat-change {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.analytics-stat-card .stat-change.positive {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.analytics-stat-card .stat-change.negative {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

.analytics-card .card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
}

.analytics-card .card-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.analytics-card .card-body {
  padding: 1.5rem;
}

.period-selector {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.analytics-chart {
  height: 200px;
  position: relative;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: end;
  gap: 2px;
  padding: 1rem 0;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--accent-500), var(--accent-400));
  border-radius: 2px 2px 0 0;
  min-height: 4px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  opacity: 0.8;
  transform: scaleY(1.05);
}

.chart-bar .bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: var(--text-secondary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-bar:hover .bar-value {
  opacity: 1;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.visitor-breakdown {
  display: flex;
  gap: 2rem;
}

.visitor-stat {
  flex: 1;
  text-align: center;
}

.visitor-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.visitor-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.visitor-percentage {
  font-size: 0.75rem;
  color: var(--accent-500);
  font-weight: 600;
}

.engagement-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.engagement-metric {
  text-align: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.engagement-metric i {
  font-size: 1.5rem;
  color: var(--accent-500);
  margin-bottom: 0.5rem;
}

.engagement-metric .metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.engagement-metric .metric-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.performance-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.performance-metric {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 8px;
}

.performance-metric .metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--accent-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-600);
}

.performance-metric .metric-content {
  flex: 1;
}

.performance-metric .metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.performance-metric .metric-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.performance-metric .metric-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
}

.performance-metric .metric-status.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.performance-metric .metric-status.good {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.performance-metric .metric-status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.country-item, .referrer-item, .device-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.country-item:last-child, .referrer-item:last-child, .device-item:last-child {
  border-bottom: none;
}

.country-info, .referrer-info, .device-info {
  flex: 1;
}

.country-name, .referrer-name, .device-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.country-count, .referrer-count, .device-count {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.country-bar {
  width: 60px;
  height: 4px;
  background: var(--bg-secondary);
  border-radius: 2px;
  overflow: hidden;
}

.country-fill {
  height: 100%;
  background: var(--accent-500);
  border-radius: 2px;
}

.country-percentage, .referrer-percentage, .device-percentage {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

.referrer-icon, .device-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.analytics-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

/* Analytics Mobile Responsive */
@media (max-width: 768px) {
  .analytics-modal {
    width: 98vw;
    max-height: 95vh;
  }

  .analytics-overview {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .visitor-breakdown {
    flex-direction: column;
    gap: 1rem;
  }

  .engagement-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .performance-grid {
    gap: 0.75rem;
  }

  .analytics-actions {
    flex-direction: column;
  }
}
