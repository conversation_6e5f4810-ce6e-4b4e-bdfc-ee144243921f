<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Dashboard - PortfolioPro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <h2 class="text-gradient">PortfolioPro Client</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li><a href="client-dashboard.html" class="nav-link active">Dashboard</a></li>
                    <li><a href="#" class="nav-link">Browse Freelancers</a></li>
                    <li><a href="#" class="nav-link">My Projects</a></li>
                    <li><a href="#" class="nav-link">Messages</a></li>
                    <li><a href="#" class="nav-link">Payments</a></li>
                </ul>
            </div>
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="Client" class="user-avatar">
                    <span class="user-name" id="clientName">Client</span>
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">Profile</a>
                        <a href="#" class="dropdown-item">Settings</a>
                        <a href="#" class="dropdown-item logout-btn">Logout</a>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>
    </header>

    <!-- Client Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="welcome-section">
                    <h1>Welcome back, <span id="welcomeName">Client</span> 👋</h1>
                    <p>Manage your projects and find the perfect freelancers</p>
                </div>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="showPostProjectModal()">
                        <i class="fas fa-plus"></i>
                        Post New Project
                    </button>
                    <button class="btn btn-outline" onclick="showBrowseFreelancers()">
                        <i class="fas fa-search"></i>
                        Browse Freelancers
                    </button>
                </div>
            </div>

            <!-- Freelancer Search Section -->
            <div class="search-section">
                <div class="search-container">
                    <div class="search-header">
                        <h2><i class="fas fa-search"></i> Find Freelancers</h2>
                        <p>Search and browse talented freelancers for your projects</p>
                    </div>

                    <div class="search-input-group">
                        <i class="fas fa-search"></i>
                        <input type="text" id="freelancerSearch" placeholder="Search freelancers by name, skills, or expertise..." class="search-input">
                    </div>

                    <div class="search-filters">
                        <select id="skillFilter" class="filter-select">
                            <option value="">All Skills</option>
                            <option value="web-development">Web Development</option>
                            <option value="mobile-development">Mobile Development</option>
                            <option value="ui-ux-design">UI/UX Design</option>
                            <option value="graphic-design">Graphic Design</option>
                            <option value="digital-marketing">Digital Marketing</option>
                            <option value="content-writing">Content Writing</option>
                            <option value="data-analysis">Data Analysis</option>
                            <option value="photography">Photography</option>
                        </select>
                        <select id="experienceFilter" class="filter-select">
                            <option value="">All Experience</option>
                            <option value="entry">Entry Level (0-2 years)</option>
                            <option value="mid">Mid Level (2-5 years)</option>
                            <option value="senior">Senior Level (5+ years)</option>
                        </select>
                        <select id="availabilityFilter" class="filter-select">
                            <option value="">All Availability</option>
                            <option value="available">Available Now</option>
                            <option value="busy">Busy</option>
                        </select>
                        <button id="clearFilters" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Clear Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Freelancers Grid -->
            <div class="freelancers-section">
                <div class="section-header">
                    <h2>Available Freelancers</h2>
                    <div class="view-options">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <div id="freelancersGrid" class="freelancers-grid">
                    <!-- Freelancer cards will be populated by JavaScript -->
                </div>

                <div id="noResults" class="no-results" style="display: none;">
                    <i class="fas fa-search"></i>
                    <h3>No freelancers found</h3>
                    <p>Try adjusting your search criteria or filters</p>
                </div>
            </div>

            <!-- Client Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card glass-card">
                    <div class="stat-icon client-stat">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeProjects">0</h3>
                        <p>Active Projects</p>
                        <span class="stat-change positive">+2 this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon client-stat">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="hiredFreelancers">0</h3>
                        <p>Hired Freelancers</p>
                        <span class="stat-change positive">+3 this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon client-stat">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalSpent">$0</h3>
                        <p>Total Spent</p>
                        <span class="stat-change positive">+25% this month</span>
                    </div>
                </div>
                
                <div class="stat-card glass-card">
                    <div class="stat-icon client-stat">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="completedProjects">0</h3>
                        <p>Completed Projects</p>
                        <span class="stat-change positive">+1 this week</span>
                    </div>
                </div>
            </div>

            <!-- Client Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Active Projects -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Active Projects</h3>
                        <a href="#" class="view-all">View All</a>
                    </div>
                    <div class="card-body">
                        <div id="activeProjectsList" class="project-list">
                            <div class="no-data">No active projects. <a href="#" onclick="showPostProjectModal()">Post your first project!</a></div>
                        </div>
                    </div>
                </div>

                <!-- Recent Proposals -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Proposals</h3>
                        <span class="badge" id="proposalCount">0</span>
                    </div>
                    <div class="card-body">
                        <div id="proposalsList" class="proposal-list">
                            <div class="no-data">No proposals yet</div>
                        </div>
                    </div>
                </div>

                <!-- Recommended Freelancers -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recommended Freelancers</h3>
                        <a href="#" class="view-all" onclick="showBrowseFreelancers()">Browse All</a>
                    </div>
                    <div class="card-body">
                        <div id="recommendedFreelancers" class="freelancer-recommendations">
                            <!-- Recommended freelancers will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Recent Messages -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Messages</h3>
                        <a href="#" class="view-all">View All</a>
                    </div>
                    <div class="card-body">
                        <div id="messagesList" class="message-list">
                            <div class="no-data">No messages yet</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Post Project Modal -->
    <div id="postProjectModal" class="modal">
        <div class="modal-content large-modal">
            <span class="close" onclick="closeModal('postProjectModal')">&times;</span>
            <h2>Post New Project</h2>
            <form id="postProjectForm">
                <div class="form-group">
                    <label>Project Title</label>
                    <input type="text" name="title" class="form-input" required placeholder="e.g., Build a responsive website">
                </div>
                
                <div class="form-group">
                    <label>Project Description</label>
                    <textarea name="description" class="form-input" rows="5" required placeholder="Describe your project in detail..."></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Budget Range</label>
                        <select name="budget" class="form-input" required>
                            <option value="">Select budget range</option>
                            <option value="500-1000">$500 - $1,000</option>
                            <option value="1000-2500">$1,000 - $2,500</option>
                            <option value="2500-5000">$2,500 - $5,000</option>
                            <option value="5000+">$5,000+</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Project Duration</label>
                        <select name="duration" class="form-input" required>
                            <option value="">Select duration</option>
                            <option value="1-2 weeks">1-2 weeks</option>
                            <option value="3-4 weeks">3-4 weeks</option>
                            <option value="1-2 months">1-2 months</option>
                            <option value="3+ months">3+ months</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Required Skills</label>
                    <input type="text" name="skills" class="form-input" placeholder="e.g., React, Node.js, MongoDB (comma separated)">
                </div>
                
                <div class="form-group">
                    <label>Project Category</label>
                    <select name="category" class="form-input" required>
                        <option value="">Select category</option>
                        <option value="web-development">Web Development</option>
                        <option value="mobile-development">Mobile Development</option>
                        <option value="ui-ux-design">UI/UX Design</option>
                        <option value="digital-marketing">Digital Marketing</option>
                        <option value="content-writing">Content Writing</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" onclick="closeModal('postProjectModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Post Project</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Browse Freelancers Modal -->
    <div id="browseFreelancersModal" class="modal">
        <div class="modal-content large-modal">
            <span class="close" onclick="closeModal('browseFreelancersModal')">&times;</span>
            <h2>Browse Freelancers</h2>
            <div class="freelancer-filters">
                <div class="filter-row">
                    <select id="skillFilter" class="form-input">
                        <option value="">All Skills</option>
                        <option value="web-development">Web Development</option>
                        <option value="ui-ux-design">UI/UX Design</option>
                        <option value="digital-marketing">Digital Marketing</option>
                    </select>
                    <select id="ratingFilter" class="form-input">
                        <option value="">All Ratings</option>
                        <option value="4.5+">4.5+ Stars</option>
                        <option value="4.0+">4.0+ Stars</option>
                        <option value="3.5+">3.5+ Stars</option>
                    </select>
                </div>
            </div>
            <div id="freelancersList" class="freelancers-grid">
                <!-- Freelancers will be loaded here -->
            </div>
        </div>
    </div>

    <script src="scripts/auth.js"></script>
    <script src="scripts/client-dashboard.js"></script>
</body>
</html>
