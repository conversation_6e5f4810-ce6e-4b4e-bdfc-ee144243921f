<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Portfolio Pro</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.html">
                    <i class="fas fa-briefcase"></i>
                    Portfolio Pro
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <i class="fas fa-key"></i>
                    <h1>Reset Your Password</h1>
                    <p>Enter your new password below</p>
                </div>

                <div id="resetContent" class="auth-content">
                    <form id="resetPasswordForm">
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <div class="password-input-container">
                                <input type="password" id="newPassword" name="newPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="passwordStrength"></div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">Confirm New Password</label>
                            <div class="password-input-container">
                                <input type="password" id="confirmPassword" name="confirmPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="password-requirements">
                            <h4>Password Requirements:</h4>
                            <ul>
                                <li id="req-length">At least 8 characters</li>
                                <li id="req-upper">One uppercase letter</li>
                                <li id="req-lower">One lowercase letter</li>
                                <li id="req-number">One number</li>
                                <li id="req-special">One special character</li>
                            </ul>
                        </div>

                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-save"></i>
                            Reset Password
                        </button>
                    </form>
                </div>

                <div class="auth-footer">
                    <p>Remember your password? <a href="index.html">Back to Login</a></p>
                </div>
            </div>
        </div>
    </main>

    <script src="scripts/auth.js"></script>
    <script>
        let resetToken = null;

        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            resetToken = urlParams.get('token');

            if (!resetToken) {
                showError('Invalid reset link. Please request a new password reset.');
                return;
            }

            setupFormHandlers();
        });

        function setupFormHandlers() {
            const form = document.getElementById('resetPasswordForm');
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            // Password strength indicator
            newPasswordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
                validatePasswordMatch();
            });

            confirmPasswordInput.addEventListener('input', validatePasswordMatch);

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (newPassword !== confirmPassword) {
                    showMessage('Passwords do not match.', 'error');
                    return;
                }

                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
                submitBtn.disabled = true;

                try {
                    const result = await authSystem.resetPassword(resetToken, newPassword);
                    if (result.success) {
                        showSuccess(result.message);
                    } else {
                        showMessage(result.message, 'error');
                    }
                } catch (error) {
                    showMessage('An error occurred. Please try again.', 'error');
                } finally {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });
        }

        function checkPasswordStrength(password) {
            const requirements = {
                length: password.length >= 8,
                upper: /[A-Z]/.test(password),
                lower: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            // Update requirement indicators
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                if (element) {
                    element.className = requirements[req] ? 'met' : '';
                }
            });

            // Update strength indicator
            const strengthDiv = document.getElementById('passwordStrength');
            const metCount = Object.values(requirements).filter(Boolean).length;
            
            let strength = '';
            let className = '';
            
            if (metCount < 3) {
                strength = 'Weak';
                className = 'weak';
            } else if (metCount < 5) {
                strength = 'Medium';
                className = 'medium';
            } else {
                strength = 'Strong';
                className = 'strong';
            }
            
            strengthDiv.textContent = `Password Strength: ${strength}`;
            strengthDiv.className = `password-strength ${className}`;
        }

        function validatePasswordMatch() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const confirmInput = document.getElementById('confirmPassword');

            if (confirmPassword && newPassword !== confirmPassword) {
                confirmInput.setCustomValidity('Passwords do not match');
            } else {
                confirmInput.setCustomValidity('');
            }
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const button = input.nextElementSibling;
            const icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        function showSuccess(message) {
            const contentDiv = document.getElementById('resetContent');
            contentDiv.innerHTML = `
                <div class="reset-success">
                    <i class="fas fa-check-circle"></i>
                    <h2>Password Reset Successfully!</h2>
                    <p>${message}</p>
                    <div class="auth-actions">
                        <a href="index.html" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i>
                            Go to Login
                        </a>
                    </div>
                </div>
            `;
        }

        function showError(message) {
            const contentDiv = document.getElementById('resetContent');
            contentDiv.innerHTML = `
                <div class="reset-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h2>Reset Failed</h2>
                    <p>${message}</p>
                    <div class="auth-actions">
                        <a href="index.html" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Back to Home
                        </a>
                    </div>
                </div>
            `;
        }

        function showMessage(message, type) {
            // Create or update message element
            let messageEl = document.querySelector('.auth-message');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.className = 'auth-message';
                const authCard = document.querySelector('.auth-card');
                authCard.insertBefore(messageEl, authCard.firstChild);
            }
            
            messageEl.textContent = message;
            messageEl.className = `auth-message ${type}`;
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 5000);
        }
    </script>

    <style>
        .auth-main {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem 1rem;
        }

        .auth-container {
            width: 100%;
            max-width: 500px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-header i {
            font-size: 4rem;
            color: var(--accent-500);
            margin-bottom: 1rem;
        }

        .auth-header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .auth-header p {
            color: var(--text-secondary);
        }

        .password-input-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }

        .password-toggle:hover {
            color: var(--text-primary);
        }

        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .password-strength.weak {
            color: var(--error-500);
        }

        .password-strength.medium {
            color: var(--warning-500);
        }

        .password-strength.strong {
            color: var(--success-500);
        }

        .password-requirements {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .password-requirements h4 {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .password-requirements ul {
            margin: 0;
            padding-left: 1.5rem;
            list-style: none;
        }

        .password-requirements li {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            position: relative;
        }

        .password-requirements li::before {
            content: '✗';
            position: absolute;
            left: -1.5rem;
            color: var(--error-500);
        }

        .password-requirements li.met {
            color: var(--success-500);
        }

        .password-requirements li.met::before {
            content: '✓';
            color: var(--success-500);
        }

        .reset-success,
        .reset-error {
            text-align: center;
            padding: 2rem;
        }

        .reset-success i,
        .reset-error i {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .reset-success i {
            color: var(--success-500);
        }

        .reset-error i {
            color: var(--error-500);
        }

        .reset-success h2,
        .reset-error h2 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .auth-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            flex-wrap: wrap;
        }

        .auth-footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            text-align: center;
        }

        .auth-footer p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .auth-footer a {
            color: var(--accent-500);
            text-decoration: none;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        .auth-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .auth-message.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-600);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .auth-message.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-600);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        @media (max-width: 768px) {
            .auth-card {
                padding: 2rem;
            }

            .auth-actions {
                flex-direction: column;
            }
        }
    </style>
</body>
</html>
